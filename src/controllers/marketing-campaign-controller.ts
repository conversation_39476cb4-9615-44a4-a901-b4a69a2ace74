import moment from 'moment'
import fetch from 'cross-fetch'

import { stringify as csv_stringify_sync } from 'csv-stringify/sync'
//Import utils functions
import {
  insertLogChange,
  marketingCampaignCommon,
  marketingCampaignCheckSchedule,
  marketingCampaignSendTest,
  renameFile,
  facebookUtils,
  salesforceUtils,
  applicationUtils,
  validate,
  schoolUtils,
  campaignUtils,
  leadUtils,
  uploadLogo,
  shortUrls,
  getObjectValues,
  blacklistCommunicationUtils,
  schoolPhoneUtils,
  marketingCampaignExecuteTrigger,
  marketingCampaignParameters,
  marketingCampaignExecuteSchedule,
  applicationSetting,
} from '@utils'

import * as sendApplicationToLead  from '../utils/send-application-to-lead'

//Import error files and handlers
import { serverError } from '@errors'

//Import object schemas
import * as schemas from '@schemas'

// Import constants
import {
  MARKETING_CAMPAIGN_CONSTANTS,
  S3_BUCKET,
  FRONTEND_HOST_SEARCH,
  BLACKLIST_COMMUNICATION_TYPES,
  APACHE_AIRFLOW_URL,
  LAMBDA_DAGS_URL,
  APACHE_AIRFLOW_USERNAME,
  APACHE_AIRFLOW_PASSWORD,
  NODE_ENV
} from '@constants'

//Import interfaces
import {
  GetMarketingCampaignsRequest,
  GetMarketingCampaignsResponse,
  GetMarketingCampaignByIdRequest,
  InsertOrUpdateMarketingCampaignRequest,
  InsertMarketingCampaignParams,
  UpdateMarketingCampaignParams,
  MarketingCampaign,
  SetStatusMarketingCampaignResponse,
  SetStatusMarketingCampaignParams,
  SetStatusMarketingCampaignRequest,
  RenameMarketingCampaignRequest,
  RenameMarketingCampaignResponse,
  RenameMarketingCampaignParams,
  ChangeTypeMarketingCampaignRequest,
  ChangeTypeMarketingCampaignResponse,
  ChangeTypeMarketingCampaignParams,
  TrackViewsRequest,
  TrackViewsResponse,
  InsertOrUpdateMarketingCampaignTrackerParams,
  TrackClicksRequest,
  TrackClicksResponse,
  GetMarketingCampaignCountsRequest,
  GetMarketingCampaignCountsResponse,
  UploadEmailCampaignSchoolLogoRequest,
  UploadEmailCampaignSchoolLogoResponse,
  CheckSchedulesMarketingCampaignResponse,
  SendTestMarketingCampaignResponse,
  SendTestMarketingCampaignRequest,
  GetApplicationUrlForTestMarketingCampaignRequest,
  GetApplicationUrlForTestMarketingCampaignResponse,
  GetExecutionsByLeadIdMarketingCampaignResponse,
  GetExecutionsByLeadIdMarketingCampaignRequest,
  Pagination,
  FacebookReportByWeek,
  FacebookError,
  SObjectCase,
  CampaignReport,
  GetScholaMarketingCampaignsResponse,
  ScholaMarketingCampaignReport,
  ScholaMarketingCampaignError,
  GetScholaMarketingCampaignsRequest,
  MarketingCampaignLead,
  MarketingCampaingSourceData,
  GetExecutionsByCampaignIdRequest,
  GetExecutionsByCampaignIdResponse,
  GetAuditResponse,
  GetExecutionsByIdRequest,
} from '@interfaces'

// Import queries
import { insertQuery, selectQuery, marketing_campaigns_queries as mcQueries, leads_queries as lQueries } from '@queries'

// Import airflow connector
import { getDag, stopAllDagRuns } from '@connectors/airflow'

// Import postgres connector
import postgres from '@connectors/postgres'

/**
 * Marketing Campaign controller functions
 * Used by /src/routes/marketing-campaign/school-marketing-campaigns.ts
 */

/**
 * Gets the marketing campaigns by some filters like schoolId, platform and with pagination
 * @param {GetMarketingCampaignsRequest} request
 * @param {number} request.school_id school's id
 * @param {number} request.page  page number
 * @param {number} request.pageSize  page size
 * @param {string} request.platform  platform "email", "sms"
 * @param {boolean} request.archived archived
 * @returns Promise<GetMarketingCampaignsResponse> response object with marketing campaigns
 */
export async function getMarketingCampaigns(request: GetMarketingCampaignsRequest): Promise<GetMarketingCampaignsResponse> {
  await validate(request, schemas.get_marketing_campaigns)
  let response: GetMarketingCampaignsResponse = undefined
  try {
    const school_id = request.school_id
    const page = request.page || 1
    const page_size =  request.pageSize || 10
    const platform = request.platform || MARKETING_CAMPAIGN_CONSTANTS.PLATFORMS.EMAIL
    const archived = request.archived || false
    const sortField = request.sortField || ''
    const sortDirection = request.sortDirection || ''
    const filters = request.filters || []
    response = await marketingCampaignCommon.getMarketingCampaigns(school_id, platform, archived, page, page_size, sortField, sortDirection, filters)
  } catch (error) {
    console.error('marketing-campaign.getMarketingCampaigns() > error', error)
    throw serverError
  }
  return response
}

/**
 * Gets a marketing campaign by id
 * @param {GetMarketingCampaignByIdRequest} request
 * @param {number} request.school_id shool's id
 * @param {number} request.campaign_id marketing campaign's id
 * @returns Promise<MarketingCampaign> response object of marketing campaign
 */
export async function getMarketingCampaignById(request: GetMarketingCampaignByIdRequest): Promise<MarketingCampaign> {
  await validate(request, schemas.get_marketing_campaign_by_id)
  let response: MarketingCampaign = undefined
  try {
    response = await marketingCampaignCommon.getMarketingCampaignById(request.campaign_id)
  } catch (error) {
    console.error('marketing-campaign.getMarketingCampaignById() > error', error)
    throw serverError
  }
  return response
}

/**
 * Insertion or update of marketing campaign
 * @param {InsertOrUpdateMarketingCampaignRequest} request
 * @param {number} request.id marketing campaign's id
 * @param {number} request.school_id school's id
 * @param {string} request.platform platform "email", "sms"
 * @param {string} request.type type of marketing campaign "Recurring Automated Email", "Regular Mass Email", "Recurring Automated", "Regular Mass"
 * @param {string} request.name marketing campaign's name
 * @param {string} request.audience_language audience language "all", "en", "es"
 * @param {number} request.layout_id layout's id
 * @param {number} request.marketing_template_id marketing template's id
 * @param {Dictionary<string, string>[]} request.params array of params
 * @returns Promise<MarketingCampaign> response object updated of marketing campaign
 */
export async function insertOrUpdateMarketingCampaign(request: InsertOrUpdateMarketingCampaignRequest, user_id: string): Promise<MarketingCampaign>{
  await validate(request, schemas.insert_or_update_marketing_campaign)

  let response: MarketingCampaign = undefined

  const marketing_campaign_id = request.id || -1

  try {
    const marketing_campaign = await marketingCampaignCommon.getMarketingCampaignById(marketing_campaign_id)
    if(!marketing_campaign){
      //insert
      const insert_params: InsertMarketingCampaignParams = {
        school_id: request.school_id,
        status: MARKETING_CAMPAIGN_CONSTANTS.STATUSES.DRAFT,
        platform: request.platform,
        type: request.type,
        name: request.name,
        audience_language: request.audience_language,
        layout_id: request.layout_id,
        marketing_template_id: request.marketing_template_id,
        params: JSON.stringify(request.params),
        created_at: moment.utc(),
        updated_at: moment.utc()
      }

      response = await marketingCampaignCommon.insertMarketingCampaign(insert_params)

      if(response) {
        //add log
        const log_changes_params = {
          school_id: request.school_id,
          user_id: user_id,
          table_name: 'marketing_campaigns',
          row_id: response.id,
          operation_type: 'add',
          new_values: response as unknown as Record<string, unknown>,
          created_at: moment().utc()
        }

        await insertLogChange(log_changes_params)
      }
    }
    else{
      //update
      const params = await marketingCampaignCommon.updateUrlsOnMarketingCampaignParams(request.params)

      const update_params: UpdateMarketingCampaignParams = {
        id: request.id,
        updated_at:  moment.utc(),
        platform: request.platform,
        type: request.type,
        name: request.name,
        audience_language: request.audience_language,
        layout_id: request.layout_id,
        marketing_template_id: request.marketing_template_id,
        params: JSON.stringify(params),
      }

      response = await marketingCampaignCommon.updateMarketingCampaign(update_params)

      if(response){
        //add log
        const log_changes_params = {
          school_id: request.school_id,
          user_id: user_id,
          table_name: 'marketing_campaigns',
          row_id: response.id,
          operation_type: 'edit',
          new_values: response as unknown as Record<string, unknown>,
          old_values: marketing_campaign as unknown as Record<string, unknown>,
          created_at: moment().utc()
        }

        await insertLogChange(log_changes_params)
      }
    }
  } catch (error) {
    console.error('marketing-campaign.insertOrUpdateMarketingCampaign() > error', error)
    throw serverError
  }
  return response
}

/**
 * Sets the status to marketing campaign
 * @param {SetStatusMarketingCampaignRequest} request
 * @param {number} request.campaign_id marketing campaign's id
 * @param {number} request.school_id school's id
 * @param {string} request.status marketing campaign's status "Draft", "In Queue", "Active", "Paused", "Finished", "Cancelled", "Archived"
 * @returns Promise<SetStatusMarketingCampaignResponse> response object with the result
 */
export async function setStatusMarketingCampaign(request: SetStatusMarketingCampaignRequest, user_id: string): Promise<SetStatusMarketingCampaignResponse> {
  await validate(request, schemas.set_status_marketing_campaign)
  const response: SetStatusMarketingCampaignResponse = {
    success: false,
  }
  try {
    const update_payload: SetStatusMarketingCampaignParams = {
      id: request.campaign_id,
      status: request.status,
      updated_at: moment.utc()
    }
    let haveDag =false
    try {
      const dag_res = await managingDags(request)
      if (dag_res && request.status === MARKETING_CAMPAIGN_CONSTANTS.STATUSES.ACTIVE) {
        update_payload['dag_id'] = `${request.school_id}_${request.campaign_id}`
        haveDag=true
      }
    } catch (error) {
      console.error('error creating dag', error)
      serverError.message = 'Error creating dag mkt campaign status not changed'
      throw serverError
    }
    const campaign = await marketingCampaignCommon.getMarketingCampaignById(request.campaign_id)
    const _response =await marketingCampaignCommon.setStatusMarketingCampaign(update_payload)
    if(_response){
      //add log
      const log_changes_params = {
        school_id: request.school_id,
        user_id: user_id,
        table_name: 'marketing_campaigns',
        row_id: request.campaign_id,
        operation_type: 'edit',
        new_values: _response as unknown as Record<string, unknown>,
        old_values: campaign as unknown as Record<string, unknown>,
        created_at: moment().utc()
      }

      await insertLogChange(log_changes_params)
    }
    //if the campaign is set to run on publish
    console.log('request.status:', request.status)
    console.log('campaign.status:', campaign.status)

    if (request.status === MARKETING_CAMPAIGN_CONSTANTS.STATUSES.ACTIVE && campaign.status === MARKETING_CAMPAIGN_CONSTANTS.STATUSES.DRAFT && haveDag) {

      if(campaign.params && campaign.params.length > 0){
        const run_on_publish = marketingCampaignParameters.getParamterValueFromParams(MARKETING_CAMPAIGN_CONSTANTS.PARAMETERS.RUN_ON_PUBLISH, campaign.params)
        if(run_on_publish == 'true'){
          console.log('run_on_publish, campaign:', campaign.id)
          let dag : any = null
          let attempts = 0
          const maxAttempts = 30

          while (!dag && attempts < maxAttempts) {
            attempts++
            dag = await getDag(campaign.dag_id)
            console.log('dag:', dag)
            if(dag && dag.data && dag.data.status === 404) dag = null
            if (!dag && attempts < maxAttempts) {
              // Wait 5 seconds before next attempt
              await new Promise(resolve => setTimeout(resolve, 5000))
            }
          }
          console.log('dag end cycle:', dag)
          if (dag) {
            const enable_logs = await applicationSetting.getMarketingCampaignEnableLogs()
            await marketingCampaignExecuteSchedule.executeSchedule(campaign, moment().format(MARKETING_CAMPAIGN_CONSTANTS.FORMAT_DATE_TIME), false, enable_logs)
          } else {
            console.error(`Failed to get DAG after ${maxAttempts} attempts for campaign ${campaign.id}`)
          }

        }
      }
    }
    response.success = true
  } catch (error) {
    console.error('marketing-campaign.setStatusMarketingCampaign() > error', error)
    throw serverError
  }
  return response
}

/**
 * Rename a marketing campaign
 * @param {RenameMarketingCampaignRequest} request
 * @param {number} request.campaign_id marketing campaign's id
 * @param {number} request.school_id school's id
 * @param {string} request.name marketing campaign's name
 * @returns Promise<RenameMarketingCampaignResponse> response object with the result
 */
export async function renameMarketingCampaign(request: RenameMarketingCampaignRequest): Promise<RenameMarketingCampaignResponse> {
  await validate(request, schemas.rename_marketing_campaign)
  const response: RenameMarketingCampaignResponse = {
    success: false,
  }
  try {
    const update_payload: RenameMarketingCampaignParams = {
      id: request.campaign_id,
      name: request.name,
      updated_at: moment.utc()
    }
    await marketingCampaignCommon.renameMarketingCampaign(update_payload)
    response.success = true
  } catch (error) {
    console.error('marketing-campaign.renameMarketingCampaign() > error', error)
    throw serverError
  }
  return response
}

/**
 * Changes the type a marketing campaign
 * @param {ChangeTypeMarketingCampaignRequest} request
 * @param {number} request.campaign_id marketing campaign's id
 * @param {number} request.school_id school's id
 * @param {string} request.type type of marketing campaign "Recurring Automated Email", "Regular Mass Email", "Recurring Automated", "Regular Mass"
 * @returns Promise<ChangeTypeMarketingCampaignResponse> return an object with the result
 */
export async function changeTypeMarketingCampaign(request: ChangeTypeMarketingCampaignRequest): Promise<ChangeTypeMarketingCampaignResponse> {
  await validate(request, schemas.change_type_marketing_campaign)
  const response: ChangeTypeMarketingCampaignResponse = {
    success: false,
  }
  try {
    const update_payload: ChangeTypeMarketingCampaignParams = {
      id: request.campaign_id,
      type: request.type,
      status: MARKETING_CAMPAIGN_CONSTANTS.STATUSES.DRAFT,
      updated_at: moment.utc()
    }
    await marketingCampaignCommon.changeTypeMarketingCampaign(update_payload)
    response.success = true
  } catch (error) {
    console.error('marketing-campaign.changeTypeMarketingCampaign() > error', error)
    throw serverError
  }
  return response
}

/**
 * Gets the schola marketing campaigns info
 * @param {number} school_id
 * @param {Pagination} pagination_options
 * @param {number} pagination_options.page
 * @param {number} pagination_options.pageSize
 * @param {number} pagination_options.rowCount
 * @param {number} pagination_options.pageCount
 * @returns { Promise<GetScholaMarketingCampaignsResponse | ScholaMarketingCampaignError>} a object of GetScholaMarketingCampaignsResponse or ScholaMarketingCampaignError
 */
export async function getScholaMarketingCampaigns(request: GetScholaMarketingCampaignsRequest): Promise<GetScholaMarketingCampaignsResponse | ScholaMarketingCampaignError> {
  await validate(request, schemas.get_schola_marketing_campaigns)
  const build_response_function = async (school_id: string | number, reports_param: void | FacebookReportByWeek[] | CampaignReport[] | FacebookError , case_param: SObjectCase) => {
    try {
      const facebook_error =  reports_param as FacebookError
      if(!facebook_error && facebook_error.error){
        return
      }

      const reports = reports_param as (FacebookReportByWeek | CampaignReport)[]
      if (!reports || reports.length === 0) {
        return
      }

      const facebook_reports = reports as FacebookReportByWeek[]
      let total_leads_received = 0, cost_per_lead_campaign = 0, application_received_manual = 0, total_added_revenue = 0, cpl_weighted = 0
      let case_id: string, campaign_start_date: string, campaign_end_date: string, updated_at: Date

      // if the report does not exist then request and the lifetime was calculated
      const idx = facebook_reports.findIndex(r => r && r.isLifetime)
      if (idx > -1) {
        case_id = facebook_reports[0].case_id
        campaign_start_date = facebook_reports[0].campaig_end_date
        campaign_end_date = facebook_reports[0].campaig_end_date

        const lifetime = facebook_reports[idx]
        total_leads_received = lifetime.total_leads_received
        cost_per_lead_campaign = lifetime.cost_per_lead
        application_received_manual = 0 //lifetime not have apps manual
        total_added_revenue = lifetime.total_added_revenue
        updated_at = lifetime.updated_at
      } else {
        // there is not a lifetime because the data coming from db
        const campaign_reports =  reports as CampaignReport[]
        case_id = campaign_reports[0].case_id
        campaign_start_date = moment.utc(campaign_reports[0].campaig_start_date).format('YYYY-MM-DD')
        campaign_end_date = moment.utc(campaign_reports[0].campaig_end_date|| moment()).format('YYYY-MM-DD')

        campaign_reports.map(elem => {
          total_leads_received += elem.total_leads_received != null ? Number(elem.total_leads_received) : 0
          total_added_revenue += elem.total_added_revenue != null ? Number(elem.total_added_revenue) : 0
          application_received_manual += elem.application_received_manual != null ? Number(elem.application_received_manual) : 0
          cpl_weighted += (elem.total_leads_received * elem.cost_per_lead)
        })

        if (total_leads_received > 0)
          cost_per_lead_campaign = cpl_weighted / total_leads_received

        updated_at = campaign_reports[0].end_date
      }

      const applications =  await applicationUtils.getApplicationsReceivedCount({ school_id: Number(school_id), start_date: campaign_start_date, end_date: campaign_end_date})
      const report = {
        name: case_param.Subject,
        leads: total_leads_received,
        cost_per_lead: cost_per_lead_campaign,
        application_received_manual: application_received_manual || 0,
        applications: applications.toString(), //needed to match output
        potential_revenue: total_added_revenue,
        case_id,
        status: case_param.Status,
        start_date: case_param.Campaign_Start_Date__c,
        created_at: case_param.CreatedDate,
        updated_at,
      }
      const amount_from_states_fundings = await schoolUtils.getAmountFromStatesFundings(Number(school_id))
      if(amount_from_states_fundings != null && amount_from_states_fundings > -1){
        return {
          ...report,
          potential_revenue: (amount_from_states_fundings * report.leads)
        }
      }
      else{
        return report
      }
    } catch (error) {
      console.error('marketing-campaign.build_response_function', error)
      throw serverError
    }

  }

  const getFBData = async (FB_CampaignId__c: string, FB_AccountId__c: string, _case: SObjectCase) => {
    if (!_case.Campaign_Start_Date__c || !FB_CampaignId__c || !FB_AccountId__c){
      return { Warn: 'The case ' + _case.Id + ' has missing configurations. Please contact with support' }
    }
    else{
      try {
        const payload = {
          case_id: _case.Id,
          campaign_id: FB_CampaignId__c,
          account_id: FB_AccountId__c,
          status: _case.Status,
          campaign_start_date: _case.Campaign_Start_Date__c,
          campaign_end_date: _case.Campaign_End_Date__c || moment().format('YYYY-MM-DD'),
          campaign_budget: _case.Campaign_Budget__c,
          campaign_approval_date: _case.Campaign_Approval_Date__c
        }
        const report_by_case = await facebookUtils.getReportByCase(request.school_id, payload, null, true)
        const { campaign_id, reportByWeeks } = report_by_case
        const enrich_report = await facebookUtils.getEnrichReport(campaign_id, reportByWeeks)
        const enrich_lifetime = await facebookUtils.getEnrichLifetime(enrich_report)
        return await build_response_function(request.school_id, enrich_lifetime, _case)
      } catch (error) {
        console.error('marketing-campaign.getFBData() > ERROR [getFBData]', error)
      }
    }
  }

  try {
    const schola_marketing_campaigns = await salesforceUtils.getScholaMarketingCampaigns(request.school_id.toString(), {page: request.page, pageSize: request.pageSize})
    const { rowCount, cases, FB_CampaignId__c, FB_AccountId__c } = schola_marketing_campaigns

    let reports = await Promise.all(cases.map(async (c) => {
      if (!c.Campaign_Start_Date__c)
        return { name: c.Subject, Warn: 'The case ' + c.Id + ' has not start date. Please contact support' }
      else if (c.Campaign_Start_Date__c && moment(c.Campaign_Start_Date__c) > moment()) {
        return { name: c.Subject, start_date: c.Campaign_Start_Date__c, status: 'scheduled', Warn: 'The case ' + c.Id + ' has future start date: '+c.Campaign_Start_Date__c+'. Please contact support' }
      }
      else if (c.Campaign_Start_Date__c && c.Campaign_End_Date__c) {
        const campaign_reports = await campaignUtils.getCampaignReportsBySchoolIdAndCaseId(request.school_id, c.Id)
        if (campaign_reports && campaign_reports.length > 0){
          return await build_response_function(request.school_id, campaign_reports, c)
        }
        else{
          return await getFBData(FB_CampaignId__c, FB_AccountId__c, c)
        }
      }
      else{
        return await getFBData(FB_CampaignId__c, FB_AccountId__c, c)
      }
    }))
    reports = reports.filter(val => !! val)
    const pagination:  Pagination = {
      page: request.page,
      pageSize: request.pageSize,
      rowCount: Number(rowCount),
      pageCount: Math.ceil(Number(rowCount) / request.pageSize)
    }

    return {
      results: reports as ScholaMarketingCampaignReport[],
      pagination
    }
  } catch (error) {
    console.error('marketing-campaign.getScholaMarketingCampaigns() > error', error)
    return { error }
  }
}

/**
 * Tracks views of emails of marketing campaigns
 * @param {TrackViewsRequest} request
 * @param {RequestAuth} request.request request data
 * @param {number} request.campaign_id marketing campaign's id
 * @param {string} request.identifier identifier
 * @returns Promise<TrackViewsResponse> return an object with the image that is uses to track
 */
export async function trackViews(request: TrackViewsRequest): Promise<TrackViewsResponse> {
  const _request = { ...request}
  delete _request.request
  await validate(_request, schemas.track_views)
  try {
    const lookup_data = marketingCampaignCommon.getLookupData(request.request)
    const insert_or_update_params: InsertOrUpdateMarketingCampaignTrackerParams = {
      identifier: request.identifier,
      campaign_id: request.campaign_id,
      views: 1,
      region: lookup_data.region,
      city: lookup_data.city,
      browser: lookup_data.browser,
      browser_version: lookup_data.browser_version,
      os: lookup_data.os,
      os_version: lookup_data.os_version,
      device: lookup_data.device,
      url: '',
      device_version: lookup_data.device_version,
      ip: lookup_data.ip,
      country: lookup_data.country,
      user_agent: lookup_data.user_agent
    }

    await marketingCampaignCommon.insertOrUpdateMarketingCampaignTracker(insert_or_update_params)
    const buffer_pixel = Buffer.from(MARKETING_CAMPAIGN_CONSTANTS.TRACKING_PIXEL.STRING_BASE_64, 'base64')
    const response: TrackViewsResponse = {
      image: buffer_pixel,
      content_type: MARKETING_CAMPAIGN_CONSTANTS.TRACKING_PIXEL.CONTENT_TYPE
    }
    return response
  } catch (error) {
    console.error('marketing-campaign.trackViews() > error', error)
    throw serverError
  }
}

/**
 * Tracks clicks on emails of marketing campaigns
 * @param {TrackClicksRequest} request
 * @param {RequestAuth} request.request request data
 * @param {number} request.campaign_id marketing campaign's id
 * @param {string} request.identifier identifier
 * @param {string} request.location url where clicked
 * @returns Promise<TrackViewsResponse> return an object with the url of link
 */
export async function trackClicks(request: TrackClicksRequest): Promise<TrackClicksResponse> {
  const _request = { ...request}
  delete _request.request
  await validate(_request, schemas.track_clicks)
  try {
    let url = request.location || ''
    url = url.trim()
    if(url.toLowerCase().startsWith('http://')){
      const reg_ex = new RegExp('http://', 'ig')
      url = url.replace(reg_ex, 'http://')
    }
    else if(url.toLowerCase().startsWith('https://')){
      const reg_ex = new RegExp('https://', 'ig')
      url = url.replace(reg_ex, 'https://')
    }
    if(url.toLowerCase().startsWith('http') === false){
      url =`http://${url}`
    }
    const url_type = request.url_type || ''
    if(url_type !== 'test'){
      const lookup_data = await marketingCampaignCommon.getLookupData(request.request)
      const insert_or_update_params: InsertOrUpdateMarketingCampaignTrackerParams = {
        identifier: request.identifier,
        campaign_id: request.campaign_id,
        clicks: 1,
        region: lookup_data.region,
        city: lookup_data.city,
        browser: lookup_data.browser,
        browser_version: lookup_data.browser_version,
        os: lookup_data.os,
        os_version: lookup_data.os_version,
        device: lookup_data.device,
        url: url,
        device_version: lookup_data.device_version,
        ip: lookup_data.ip,
        country: lookup_data.country,
        url_type: url_type,
        user_agent: lookup_data.user_agent
      }
      await marketingCampaignCommon.insertOrUpdateMarketingCampaignTracker(insert_or_update_params)
    }

    // get short url
    if(url.includes('schola.com/s/')) {
      const _url=url.split('/s/')
      const uuid = _url[1]
      const result = await shortUrls.getLongURLByUUID(uuid)
      if(result){
        url = result.url
      } else {
        url=FRONTEND_HOST_SEARCH
      }
    }
    const response: TrackClicksResponse = {
      url_to_redirect_to: url
    }
    return response
  } catch (error) {
    console.error('marketing-campaign.trackViews() > error', error)
    throw serverError
  }
}

/**
 * Gets counts of marketing campaigns
 * @param {GetMarketingCampaignCountsRequest} request
 * @param {number} request.school_id school's id
 * @param {string} request.ids ids
 * @param {string} request.filtering filters
 * @returns Promise<TrackViewsResponse> return an object with the result of counts of marketing campaigns
 */
export async function getMarketingCampaignCounts(request: GetMarketingCampaignCountsRequest): Promise<GetMarketingCampaignCountsResponse> {
  await validate(request, schemas.get_marketing_campaign_counts)
  const response:  GetMarketingCampaignCountsResponse = {}
  try {
    const ids = request.ids.split(',')
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const filtering: any = JSON.parse(request.filtering)
    let filters
    let archived = false
    await Promise.all(ids.map(async id => {
      switch (id) {
      case MARKETING_CAMPAIGN_CONSTANTS.MARKETING_CAMPAIGN_COUNT_TYPES.CampaignBreakdown:
        response.campaignBreakdown = await leadUtils.getLeadSourceCostCount(request.school_id)
        break
      case MARKETING_CAMPAIGN_CONSTANTS.MARKETING_CAMPAIGN_COUNT_TYPES.ScholaMarketing:
        response.scholaMarketing = await salesforceUtils.getCaseCampaignCount(request.school_id.toString())
        break
      case MARKETING_CAMPAIGN_CONSTANTS.MARKETING_CAMPAIGN_COUNT_TYPES.CampaignEmail:
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        filters = filtering.find((f:any) => f.campaignEmail)
        filters = !filters ? {} : filters.campaignEmail
        archived = filters.archived || false
        response.campaignEmail = await marketingCampaignCommon.getMarketingCampaignsCount(request.school_id, MARKETING_CAMPAIGN_CONSTANTS.PLATFORMS.EMAIL, archived, [])
        break
      case MARKETING_CAMPAIGN_CONSTANTS.MARKETING_CAMPAIGN_COUNT_TYPES.CampaignSMS:
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        filters = filtering.find((f:any) => f.campaignSMS)
        filters = !filters ? {} : filters.campaignSMS
        archived = filters.archived || false
        response.campaignSMS = await marketingCampaignCommon.getMarketingCampaignsCount(request.school_id, MARKETING_CAMPAIGN_CONSTANTS.PLATFORMS.SMS, archived, [])
        break
      default:
        response[id] = 0
        break
      }
    }))
  } catch (error) {
    console.error('marketing-campaign.getMarketingCampaignCounts() > error', error)
    throw serverError
  }
  return response
}

/**
 * Upload logo for marketing campaign
 * @param {UploadEmailCampaignSchoolLogoRequest} request
 * @param {number} request.school_id school's id
 * @param {number} request.campaign_id marketing campaign's id
 * @param {string} request.fileName file name
 * @param {Buffer} request.file file
 * @param {Buffer} request.original original file
 * @returns Promise<UploadEmailCampaignSchoolLogoResponse> return an object with the url of the new image
 */
export async function uploadEmailCampaignSchoolLogo(request: UploadEmailCampaignSchoolLogoRequest): Promise<UploadEmailCampaignSchoolLogoResponse> {
  await validate(request, schemas.upload_email_campaign_school_logo)
  try {
    const { school_id, campaign_id: marketing_campaign_id, fileName, file, original } = request
    const renamed_file = renameFile(fileName)
    const  upload_logo_result = await uploadLogo( {
      original,
      subfolder: `school/${school_id}/email-campaign/${marketing_campaign_id}`,
      filename: renamed_file,
      alt_img: file,
    }, {
      url: `https://${S3_BUCKET}.s3.amazonaws.com`,
      disable_random: false
    }
    )
    return {url: upload_logo_result.url}
  } catch (error) {
    console.error('marketing-campaign.uploadEmailCampaignSchoolLogo() > error', error)
    throw serverError
  }
}

/**
 * Checks the marketing campaigns with schedules and executes them if the criteria are met
 * @returns Promise<CheckSchedulesMarketingCampaignResponse> return a object with the result
 */
export async function checkSchedules(): Promise<CheckSchedulesMarketingCampaignResponse> {
  const response : CheckSchedulesMarketingCampaignResponse = {
    success: false,
    message: []
  }
  try {
    const check_schedules_result = await marketingCampaignCheckSchedule.checkSchedules()
    if(check_schedules_result){
      response.success = check_schedules_result.success
      response.message = check_schedules_result.result
    }

  } catch (error) {
    console.error('marketing-campaign.checkSchedules() > error', error)
    throw serverError
  }
  return response
}

/**
 * Sends test of email or sms of a marketing campaign
 * @param {SendTestMarketingCampaignRequest} request
 * @param {number} request.school_id school's id
 * @param {MarketingCampaignSendTest} request.marketingCampaign marketing campaign
 * @param {LeadSendTest} request.lead lead
 * @returns Promise<SendTestMarketingCampaignResponse> return a object with the result
 */
export async function sendTest(request: SendTestMarketingCampaignRequest): Promise<SendTestMarketingCampaignResponse> {
  await validate(request, schemas.send_test)
  const response : SendTestMarketingCampaignResponse = {
    success: false,
    message: ''
  }
  try {
    const {school_id} = request
    const marketing_campaign = (request.marketingCampaign as unknown as MarketingCampaign)
    const marketing_campaign_lead = (request.lead as unknown as MarketingCampaignLead)
    const send_test_result =  await marketingCampaignSendTest.processTest(school_id, marketing_campaign, marketing_campaign_lead)
    if(send_test_result){
      response.success = send_test_result.success,
      response.message = send_test_result.message
    }

  } catch (error) {
    console.error('marketing-campaign.sendTest() > error', error)
    throw serverError
  }
  return response
}

/**
 * Gets the application's url for test
 * @param {GetApplicationUrlForTestMarketingCampaignRequest} request
 * @param {number} request.school_id school's id
 * @returns Promise<GetApplicationUrlForTestMarketingCampaignResponse> return a object with the result
 */
export async function getApplicationUrlForTest(request: GetApplicationUrlForTestMarketingCampaignRequest): Promise<GetApplicationUrlForTestMarketingCampaignResponse> {
  await validate(request, schemas.get_application_url_for_test)
  const response : GetApplicationUrlForTestMarketingCampaignResponse = {
    url:''
  }
  try {
    const {school_id} = request
    const school = await marketingCampaignCommon.getSchoolById(school_id)
    const url_result =  await sendApplicationToLead.getApplicationUrlForTest(school)
    if(url_result){
      response.url = url_result
    }
  } catch (error) {
    console.error('marketing-campaign.getApplicationUrlForTest() > error', error)
    throw serverError
  }
  return response
}

/**
 * Gets the executions by lead
 * @param {GetExecutionsByLeadIdMarketingCampaignRequest} request
 * @param {number} request.school_id school's id
 * @param {number} request.lead_id lead's id
 * @returns Promise<GetExecutionsByLeadIdMarketingCampaignResponse[]> return a array of executions of marketing campaigns
 */
export async function getExecutionsByLead(request: GetExecutionsByLeadIdMarketingCampaignRequest): Promise<GetExecutionsByLeadIdMarketingCampaignResponse[]>{
  await validate(request, schemas.get_executions_by_lead)
  let response : GetExecutionsByLeadIdMarketingCampaignResponse[] = []
  try {
    const {school_id, lead_id} = request
    response = await marketingCampaignCommon.getExecutionsByLead(school_id, lead_id)
  } catch (error) {
    console.error('marketing-campaign.getExecutionsByLead() > error', error)
    throw serverError
  }
  return response
}

/**
 * Create the email event
 * @param { any } event
 * @returns
 */
export async function createEmailEvent(event: any) {
  try {
    const params = {
      'event_id': event.id,
      'event': event.event,
      'message_id': event.message.headers['message-id'],
      'event_body': event
    }
    const values = await getObjectValues(params)
    const db_response = await postgres.query(insertQuery('email_audits_events', params), values)
    // if failed update the email_audit
    if(event.event === 'failed'){
      const response = await marketingCampaignCommon.getEmailAuditByMessageId(event.message.headers['message-id'])
      if(response && response.length > 0){
        const email_audit = response[0] as GetAuditResponse
        await marketingCampaignCommon.setStatusEmailAudit(email_audit.id, 2,event['delivery-status'].message )
      }
    }
    // add to blacklist if event is failed or unsubscribed
    if((event.event === 'failed' && event.severity === 'permanent') || event.event === 'unsubscribed'){
      const add_payload = {
        school_id: -1,
        to: event.recipient,
        reason: ( event.event === 'unsubscribed' ? 'unsubscribed' : event.reason),
        deleted: false,
        communication_type: BLACKLIST_COMMUNICATION_TYPES.EMAIL
      }

      await blacklistCommunicationUtils.addToBlacklist(add_payload)
    }
    return db_response.rows
  } catch (error) {
    console.error('createEmailEvent - error', error)
    throw serverError
  }
}

/**
 * Manage dags
 * @param { any } campaign_data
 * @returns
 */
export async function managingDags(campaign_data: any): Promise<any> {
  try {
    console.log('*****************0*****************')
    const query = selectQuery('marketing_campaigns') + ` where id=$1 and school_id=$2 and type='Advanced'`
    const db_response = await postgres.query(query, [campaign_data.campaign_id, campaign_data.school_id])
    if (db_response.rowCount == 0) return false
    console.log('*****************1 *****************')
    const campaign = db_response.rows[0]
    let url = `${APACHE_AIRFLOW_URL}/dags/`
    let payload: any = {}
    let method = 'PATCH'
    let auth = true
    let dag_id = ''

    console.log('*****************2*****************')
    if (campaign?.dag_id && campaign_data?.status === 'Active') {
      console.log('*****************reactivating dag*****************')
      url += `${campaign.dag_id}`
      payload = { 'is_paused': false }
    } else if (campaign?.dag_id && ['Archived', 'Cancelled', 'Finished', 'Paused'].includes(campaign_data?.status)) {
      console.log('*****************pausing dag*****************')
      url += `${campaign.dag_id}`
      payload = { 'is_paused': true }
      if (!campaign_data?.keep_running) {
        await stopAllDagRuns(campaign.dag_id)
      }
    } else if (campaign_data?.status == 'Active') {
      console.log('*****************creating dag*****************')
      url = LAMBDA_DAGS_URL
      auth = false
      method = 'POST'
      dag_id = `${campaign.school_id}_${campaign.id}`

      payload = {
        schoolId: String(campaign.school_id),
        campaignId: String(campaign.id),
        envRunning: NODE_ENV,
        config: {
          lead: 'default',
        },
        args: {
          owner: dag_id,
          depends_on_past: false,
        },
        params: {
          dag_id: dag_id,
          description: campaign.name,
          tags: [
            `mkt_template_id_${campaign.marketing_template_id}`,
            campaign.platform,
          ],
          dag_display_name: dag_id,
        },
        tasks: {
          nodes: [],
          edges: [],
        },
      }
      console.log('*****************3*****************')
      const edgesParam = campaign.params.find((p: any) => p.key === 'edges')
      if (edgesParam && typeof edgesParam.value === 'string') {
        try {
          payload.tasks.edges = JSON.parse(edgesParam.value).map((edge: any) => {
            if (!edge.source.startsWith('start') && !edge.target.startsWith('end')) {
              return edge
            }
          }).filter(Boolean)
        } catch (error) {
          console.error('Error parsing edges JSON:', error)
          payload.tasks.edges = []
        }
      } else if (edgesParam && Array.isArray(edgesParam.value)) {
        payload.tasks.edges = edgesParam.value
      }
      console.log('*****************4*****************')
      const nodesParam = campaign.params.find((p: any) => p.key === 'nodes')
      if (nodesParam && typeof nodesParam.value === 'string') {
        try {
          payload.tasks.nodes = JSON.parse(nodesParam.value).map((node: any) => {
            if (!node.id.startsWith('end')) {
              return node
            }
          }).filter(Boolean)
        } catch (error) {
          console.error('Error parsing nodes JSON:', error)
          payload.tasks.nodes = []
        }
      } else if (nodesParam && Array.isArray(nodesParam.value)) {
        payload.tasks.nodes = nodesParam.value
      }
      payload = { 'dag': { ...payload } }

    } else {
      console.log('*****************returning false*****************')
      return false
    }

    console.log('*****************5*****************')
    if (url) {
      const headers = {
        'Content-Type': 'application/json',
      } as any

      console.log('*****************6*****************')
      if (auth) {
        const encodedCredentials = Buffer.from(`${APACHE_AIRFLOW_USERNAME}:${APACHE_AIRFLOW_PASSWORD}`).toString('base64')
        headers['Authorization'] = `Basic ${encodedCredentials}`
      }

      console.log('*****************7*****************')
      const fetchResponse = await fetch(url, {
        method,
        headers,
        body: JSON.stringify(payload),
      })
      console.log('*****************8*****************')
      if (fetchResponse.ok) {
        try {
          await fetchResponse.json()
        } catch (jsonError) {
          console.error('Failed to parse JSON response:', jsonError)
          // Continue execution even if JSON parsing fails

        }
      } else {
        console.error('Request failed with status:', fetchResponse.status)
      }
      console.log('*****************9*****************')
      return true
    } else {
      console.log('No valid URL for this status', payload)
      return false
    }
  } catch (error) {
    console.error('managingDags error', error)
    throw serverError
  }
}

/**
 * Endpoint to receive dag calls
 * @param { any } params
 * @returns
 */
export async function callFromDag(params: any): Promise<any> {
  try {
    console.log('----------------0----------------')
    console.log(params)
    console.log('----------------1----------------')
    const campaign = await getMarketingCampaignById({
      campaign_id: params.campaign_id,
      school_id: params.school_id
    })

    console.log('----------------2----------------')
    if (!campaign) throw new Error('Campaign not found')

    let nodes = ''
    let edges = ''
    for (const node of campaign.params) {
      if (node.key === 'nodes') {
        nodes = node.value
      }
      if (node.key === 'edges') {
        edges = node.value
      }
    }

    console.log('----------------3----------------')
    const nodesJson = JSON.parse(nodes)
    const edgesJson = JSON.parse(edges)

    const condNodes: any[] = []
    for (const node of nodesJson) {
      if (params?.body?.condition_parents.includes(node.id)) {
        condNodes.push({ condition: node.data.condition, userAction: node.data.userAction })
      }
    }

    console.log('----------------4----------------')
    const lastCommunicationNode = findLastCommunicationNode(params.node_id, edgesJson)
    const comType = (lastCommunicationNode.startsWith('email')) ? 'email' : 'sms'
    const node = nodesJson.find((node: any) => node.id === params.node_id)
    node.execution_id = params.body.execution_id

    console.log('----------------5----------------')
    let leadsToSend: any[] = []
    let auditCreatedAt = ''
    if (params?.body?.lead_id > 0) {
      const query = mcQueries.getLastCommunicationNodeByLeadId(comType)
      const res = await postgres.query(query, [params.campaign_id, lastCommunicationNode, params.body.lead_id])
      if (res.rowCount === 0) {
        console.log('----------------No leads to process----------------')
        return {
          message: 'No leads to process'
        }
      }
      auditCreatedAt = res.rows[0].created_at
      leadsToSend.push(params.body.lead_id)
    } else {
      const query = mcQueries.getLastCommunicationNode(comType)
      const res = await postgres.query(query, [params.body.execution_id, params.campaign_id, lastCommunicationNode])
      if (res.rowCount === 0) {
        console.log('----------------No leads to process----------------')
        return {
          message: 'No leads to process'
        }
      }
      const leadsIds = res.rows.map((row: any) => row.lead_id)
      auditCreatedAt = res.rows[0].created_at
      leadsToSend = [...leadsIds]
    }

    const today = moment().format('YYYY-MM-DD HH:mm:ss')
    const executionTime = moment(auditCreatedAt).format('YYYY-MM-DD HH:mm:ss')

    for (const condition of condNodes) {
      const key = `${condition.userAction.toLowerCase()} ${condition.condition}`
      let res
      let query = ''

      console.log('----------------filter----------------')
      console.log(key)
      switch (key) {
      case 'successfully-receive-message true':
        if (params.body.execution_id != 0) {
          res = await postgres.query(mcQueries.returnLeadsSuccessfullyReceiveMessage(comType), [params.school_id, params.body.execution_id, params.campaign_id])
        } else {
          res = await postgres.query(mcQueries.returnLeadSuccessfullyReceiveMessage(comType), [params.body.lead_id, params.campaign_id, executionTime])
        }
        break
      case 'successfully-receive-message false':
        if (params.body.execution_id != 0) {
          res = await postgres.query(mcQueries.returnLeadsNotSuccessfullyReceiveMessage(comType), [params.school_id, params.body.execution_id, params.campaign_id])
        } else {
          res = await postgres.query(mcQueries.returnLeadNotSuccessfullyReceiveMessage(comType), [params.body.lead_id, params.campaign_id, executionTime])
        }
        break
      case 'open-last-message true':
        if (params.body.execution_id != 0) {
          res = await postgres.query(mcQueries.returnLeadsOpenLastEmail(), [params.body.execution_id, params.campaign_id, lastCommunicationNode])
        } else {
          res = await postgres.query(mcQueries.returnLeadsOpenLastEmailByLeadId(), [params.body.lead_id, params.campaign_id, lastCommunicationNode])
        }
        break
      case 'reply-last-message true':
        res = await postgres.query(mcQueries.returnLeadsOpenLastMessage(), [params.school_id, executionTime, today, comType])
        break
      case 'clicks-application-link true':
        if (comType === 'email') {
          if (params.body.execution_id != 0) {
            res = await postgres.query(mcQueries.returnLeadsClickAppLinkEmail(), [params.body.execution_id, lastCommunicationNode])
          } else {
            res = await postgres.query(mcQueries.returnLeadsClickAppLinkEmailByLeadId(), [params.body.lead_id, params.campaign_id, lastCommunicationNode])
          }
        } else {
          if (params.body.execution_id != 0) {
            res = await postgres.query(mcQueries.returnLeadsClickAppLinkSms(), [params.body.execution_id, lastCommunicationNode])
          } else {
            res = await postgres.query(mcQueries.returnLeadsClickAppLinkSmsByLeadId(), [params.body.lead_id, params.campaign_id, lastCommunicationNode])
          }
        }
        break
      case 'completes-application true':
        res = await postgres.query(mcQueries.returnLeadsCompletedApp(), [params.school_id, executionTime])
        break
      case 'clicks-custom-link true':
        if (comType === 'email') {
          if (params.body.execution_id != 0) {
            res = await postgres.query(mcQueries.returnLeadsClickCustomLinkEmail(), [params.body.execution_id, lastCommunicationNode])
          } else {
            res = await postgres.query(mcQueries.returnLeadsClickCustomLinkEmailByLeadId(), [params.body.lead_id, params.campaign_id, lastCommunicationNode])
          }
        } else {
          if (params.body.execution_id != 0) {
            res = await postgres.query(mcQueries.returnLeadsClickCustomLinkSms(), [params.body.execution_id, lastCommunicationNode])
          } else {
            res = await postgres.query(mcQueries.returnLeadsClickCustomLinkSmsByLeadId(), [params.body.lead_id, params.campaign_id, lastCommunicationNode])
          }
        }
        break
      case 'open-last-message false':
        if (params.body.execution_id != 0) {
          res = await postgres.query(mcQueries.returnLeadsNotOpenLastEmail(), [params.body.execution_id, params.campaign_id, lastCommunicationNode])
        } else {
          res = await postgres.query(mcQueries.returnLeadsNotOpenLastEmailByLeadId(), [params.body.lead_id, params.campaign_id, lastCommunicationNode])
        }
        break
      case 'reply-last-message false':
        res = await postgres.query(mcQueries.returnLeadsNotOpenLastMessage(), [params.school_id, executionTime, today, comType])
        break
      case 'clicks-application-link false':
        if (comType === 'email') {
          if (params.body.execution_id != 0) {
            res = await postgres.query(mcQueries.returnLeadsNotClickAppLinkEmail(), [params.body.execution_id, lastCommunicationNode])
          } else {
            res = await postgres.query(mcQueries.returnLeadsNotClickAppLinkEmailByLeadId(), [params.body.lead_id, params.campaign_id, lastCommunicationNode])
          }
        } else {
          if (params.body.execution_id != 0) {
            res = await postgres.query(mcQueries.returnLeadsNotClickAppLinkSms(), [params.body.execution_id, lastCommunicationNode])
          } else {
            res = await postgres.query(mcQueries.returnLeadsNotClickAppLinkSmsByLeadId(), [params.body.lead_id, params.campaign_id, lastCommunicationNode])
          }
        }
        break
      case 'completes-application false':
        res = await postgres.query(mcQueries.returnLeadsNotCompletedApp(), [params.school_id, executionTime])
        break
      case 'clicks-custom-link false':
        if (comType === 'email') {
          if (params.body.execution_id != 0) {
            res = await postgres.query(mcQueries.returnLeadsNotClickCustomLinkEmail(), [params.body.execution_id, lastCommunicationNode])
          } else {
            res = await postgres.query(mcQueries.returnLeadsNotClickCustomLinkEmailByLeadId(), [params.body.lead_id, params.campaign_id, lastCommunicationNode])
          }
        } else {
          if (params.body.execution_id != 0) {
            res = await postgres.query(mcQueries.returnLeadsNotClickCustomLinkSms(), [params.body.execution_id, lastCommunicationNode])
          } else {
            res = await postgres.query(mcQueries.returnLeadsNotClickCustomLinkSmsByLeadId(), [params.body.lead_id, params.campaign_id, lastCommunicationNode])
          }
        }
        break
      case 'unsubscribes true':
        query = mcQueries.returnLeadsUnsubscribes(comType)
        if (comType === 'email') {
          res = await postgres.query(query, [executionTime, params.school_id])
        } else {
          res = await postgres.query(query, [leadsToSend])
        }
        break
      case 'unsubscribes false':
        query = mcQueries.returnLeadsNotUnsubscribes(comType)
        if (comType === 'email') {
          res = await postgres.query(query, [executionTime, params.school_id])
        } else {
          res = await postgres.query(query, [leadsToSend])
        }
        break

      default:
        continue
      }
      const conditionMatchedLeads = res.rows.map((row) => row.lead_id)
      leadsToSend = leadsToSend.filter((leadId) => conditionMatchedLeads.includes(leadId))
    }
    console.log('----------------6----------------')
    if (leadsToSend.length === 0) {
      console.log('----------------No leads to process----------------')
      return {
        message: 'No leads to process'
      }
    }
    console.log('----------------7----------------')
    const db_response = await postgres.query(lQueries.getLeads(), [leadsToSend, params.school_id])
    const leads = db_response.rows
    console.log('----------------8----------------')
    const school = await marketingCampaignCommon.getSchoolById(params.school_id)
    console.log('----------------9----------------')
    let fromPhone: string | undefined
    const school_phones = await schoolPhoneUtils.getBySchoolId(params.school_id)
    if (school_phones && school_phones.length > 0) {
      fromPhone = school_phones[0].number
    }
    console.log('----------------10----------------')
    for (const lead of leads) {
      const marketing_campaign_lead = lead as MarketingCampaignLead
      const application_url = await sendApplicationToLead.getApplicationUrl(school, lead, undefined)
      const sourceData: MarketingCampaingSourceData = {
        marketing_campaign_lead,
        school,
        aplication_url: application_url,
      }

      await marketingCampaignExecuteTrigger.processMarketingCampaignForLead(
        campaign,
        sourceData,
        fromPhone,
        true,
        node
      )
    }
    console.log('----------------11----------------')
    return { message: 'Dag - Marketing campaign executed successfully' }
  } catch (error) {
    console.error('callFromDag error', error)
    throw serverError
  }
}

function findLastCommunicationNode(currentNode: string, edges: any[]) {
  const visited = new Set([currentNode])
  let frontier = [currentNode]

  while (frontier.length > 0) {
    const nextFrontier = []

    for (const node of frontier) {
      const predecessors = edges
        .filter(edge => edge.target === node)
        .map(edge => edge.source)

      for (const predecessor of predecessors) {
        if (!visited.has(predecessor)) {
          if (predecessor.startsWith('emailNode') || predecessor.startsWith('smsNode')) {
            return predecessor
          }
          visited.add(predecessor)
          nextFrontier.push(predecessor)
        }
      }
    }

    frontier = nextFrontier
  }
  return null
}

export async function getExecutionsByCampaignId(request: GetExecutionsByCampaignIdRequest): Promise<GetExecutionsByCampaignIdResponse[]> {
  await validate(request, schemas.get_executions_by_id)
  let response: GetExecutionsByCampaignIdResponse[] = []
  try {
    const { school_id, marketing_campaign_id } = request
    response = await marketingCampaignCommon.getExecutionsByMarketingCampaignId(school_id, marketing_campaign_id)
  } catch (error) {
    console.error('marketing-campaign.getExecutionsByCampaignId() > error', error)
    throw serverError
  }
  return response
}


export async function getAuditsTotalsByCampaignId(request: GetExecutionsByCampaignIdRequest): Promise<GetExecutionsByCampaignIdResponse[]> {
  await validate(request, schemas.get_executions_by_id)
  let response: GetExecutionsByCampaignIdResponse[] = []
  try {
    const { school_id, marketing_campaign_id } = request
    response = await marketingCampaignCommon.getAuditsTotalsByMarketingCampaignId(school_id, marketing_campaign_id)
  } catch (error) {
    console.error('marketing-campaign.getExecutionsByCampaignId() > error', error)
    throw serverError
  }
  return response
}

export async function getAuditsReport(request: GetExecutionsByCampaignIdRequest): Promise<string>{
  try {

    const toExport = await marketingCampaignCommon.getAuditsByMarketingCampaignId(request.school_id, request.marketing_campaign_id)
    // Convert timestamps to readable date format
    const formattedData = toExport.map((row: any) => ({
      ...row,
      created_at: moment(row.created_at).format('MM/DD/YYYY HH:mm:ss')
    }))
    const columns = ['type', 'lead_id', 'to', 'status', 'error', 'created_at']
    const csv_stream = csv_stringify_sync(formattedData, {columns: columns, header: true })
    return csv_stream

  } catch (error) {
    console.error('campaign-controller.getGlobalCampaignReportExport2() > error', error)
    throw serverError
  }
}

export async function getExecutionsReport(request: GetExecutionsByIdRequest): Promise<string>{
  try {

    const toExport = await marketingCampaignCommon.getAuditsByExecutionId(request.school_id, request.execution_id)
    // Convert timestamps to readable date format
    const formattedData = toExport.map((row: any) => ({
      ...row,
      created_at: moment(row.created_at).format('MM/DD/YYYY HH:mm:ss')
    }))
    const columns = ['type', 'lead_id', 'to', 'status', 'error', 'created_at']
    const csv_stream = csv_stringify_sync(formattedData, {columns: columns, header: true })
    return csv_stream

  } catch (error) {
    console.error('campaign-controller.getGlobalCampaignReportExport2() > error', error)
    throw serverError
  }
}


export async function getAuditsByCampaignId(request: GetExecutionsByCampaignIdRequest): Promise<GetAuditResponse[]> {
  await validate(request, schemas.get_executions_by_id)
  let response: GetAuditResponse[] = []
  try {
    const { school_id, marketing_campaign_id } = request
    response = await marketingCampaignCommon.getAuditsByMarketingCampaignId(school_id, marketing_campaign_id)
  } catch (error) {
    console.error('marketing-campaign.getExecutionsByCampaignId() > error', error)
    throw serverError
  }
  return response
}
