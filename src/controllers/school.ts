/* eslint-disable  @typescript-eslint/no-explicit-any */
import axios from 'axios'
import _ from 'lodash'
import moment from 'moment'
import * as uuid from 'uuid'
import { stringify as csv_stringify_sync } from 'csv-stringify/sync'
import { snakeCase } from 'lodash'

// Import auth0 connector
import * as auth0 from '@connectors/auth0-functions'
import { createSupportTicket} from '@connectors/clickup'

// Import s3 connector
import { s3Upload, deleteFile } from '@connectors/s3'

// Import user-school util
import {
  buildUpdate,
  getObjectValues,
  insertLogChange,
  getObjectValuesFromArray,
  renameFile,
  uploadLogo,
  featureSchoolUtils,
  sendEmail,
  createUpdateUserProfile,
  getSchoolUrl,
  sendSms,
  searchHistory,
  searchUtils as search,
  searchSchools,
  leadUtils,
  userSchool,
  validate,
  eventUtils,
  lookupPhone,
  schoolUtils
} from '@utils'

// Import interfaces
import type {
  School,
  Pagination,
  GetSchoolsQuery,
  PostSchool,
  PatchSchool,
  UploadSchoolLogo,
  UpdateLogo,
  UpdateSchoolFeatures,
  AddUserInSchool,
  ResendUserInSchool,
  QueryOptions,
  AddSchoolImages,
  AddSchoolCustomApplication,
  SchoolClaim,
  LocationAndPagination,
  TopOptions,
  ScheduleTour,
  Student,
  GetScheduleTour,
  UpdateScheduleTour,
  UpdateLeadTour,
  Note,
  ScheduleTourU,
  SchoolTestimonial,
  AddSchoolSeats,
  UpdateSchoolSeats,
  AddSchoolTourTimes,
  SearchSchoolV2,
  SearchSchoolFeature,
  SearchSchoolHistory,
  SearchedSchool,
  SchoolIds,
  SearchSchool,
  SchoolExternalReview,
  SchoolExternalReviews,
  ExternalReviewPayload,
  ExternalReview,
  UpdateSchoolMarketing,
  SchoolFeatureRequest,
  SaveSearch,
  EventInterested,
  BuyScholaBoostsExistMethod,
  BuyScholaBoosts,
  BuyScholaBoostsACH,
  SearchFeatures,
  UpdateScheduleTourDB,
  CreateEvent,
  AddUpdateSchoolTeacherStuffParams,
  AddOrUpdateEvent,
  FastFact,
  FastFacts,
  FastFactInsert,
  hubspotMatchSchool,
  CompletedProfileRate,
  CategoryFields,
  FAQschool,
  Question,
  FacilitySchool,
  ParamsImage,
  NewFacility,
} from '@interfaces'
import { PoolClient } from 'pg'

// import states json
import fullname_states from '../constants/fullname-states.json'

// Import error handling
import { badRequestError, conflictError, notFoundError, serverError } from '@errors'

import { postgres, transactional_client } from '@connectors/postgres'

import {
  schools_queries as queries,
  endorsement_queries,
  about_your_school_queries,
  recruiter_questions_queries,
  events_queries,
  features_queries,
  insertQuery,
  batchParametrizedInsertQuery,
  notes_queries,
  onboardings_queries,
  user_schools_queries,
  leads_queries,
  subscriptions_queries,
} from '@queries'

import {
  endorsementController,
  //salesforceController,
  usersController,
  notesController,
  leadsController,
  eventsController,
  featuresController,
} from '@controllers'

import {
  RECRUITER_PRO_ACCESS,
  REQUIRED_FIELDS_RECRUITER_FORM,
  NOT_NULL_FIELDS_RECRUITER_FORM,
  S3_BUCKET,
  S3_DOMAIN,
  GRADES,
  YOUTUBE_REGEXP,
  VIMEO_REGEXP,
  CLAIM_CODE_LIMIT_TIME,
  SF_SCHOOL_EXPORT_COLUMNS,
  COMMON_DOMAIN_LIST,
  CLAIM_SCHOOL_MESSAGE,
  TWILIO_FROM,
  STATISTICS_EXPORT_COLUMNS,
  TOURS_EXPORT_COLUMNS,
  LEADS_EXPORT_COLUMNS,
  CONNECTIONS_EXPORT_COLUMNS,
  BEST_MATCH_SIZE,
  EXTERNAL_SITE_HOUR_RANGE,
  GOOGLE_EXTERNAL_MARKS,
  GREAT_SCHOOLS_STAR_STRING,
  GREAT_SCHOOLS_REVIEW_STRING,
  FACEBOOK_RATE_CLASS,
  FACEBOOK_TOTAL_CLASS,
  NICHE_RATES,
  NICHE_STAR_STRING,
  NICHE_REVIEW_STRING,
  FEATURE_REQUEST_EMAIL,
  FROM_EMAIL,
  INFO_RECIPIENT,
  PULSE_ERROR_SMS,
  PULSE_ERROR_PHONE,
  NODE_ENV,
  ONE_TIME_PAYMENT,
  STRIPE_CONFIG,
  FRONTEND_HOST_ADMIN,
  PLAID_CONFIG,
  SCHOLA_TABLES,
  REROUTE_1,
  REROUTE_3,
  REROUTE_5,
  MIN_INTEGER,
  MAX_INTEGER,
  LOGGER,
  REQUIRED_PROFILE_COMPLETION,
} from '@constants'

import Stripe from 'stripe'
import plaid from 'plaid'

import * as schemas from '@schemas'

// Import html generator
import { businessStatus } from '@email-templates/business-status'
import { eventInterestedSchoolNotification } from '@email-templates/event-interested-school-notification'
import { schoolPurchaseScholaBoosts } from '@email-templates/school-purchase-scholaboosts'
import schoolMarketingNotification from '@email-templates/school-email-marketing-notification'
import newFeatureRequestTemplate from '@email-templates/new-feature-request'
import school_user_creation from '@email-templates/school-user-creation'
import school_new_user from '@email-templates/school-new-user'
import school_new_user_added from '@email-templates/school-new-user-added'
import school_custom_application from '@email-templates/school-custom-application'
import school_claim from '@email-templates/school-claim'
import claim_validation from '@email-templates/claim-school-code-validation'

// Create logger
import { getLogger } from '@config/logger'
const logger = (LOGGER == 'cloudwatch') ? console : getLogger({service: 'controllers/schools'})

const stripe = new Stripe(STRIPE_CONFIG?.secret, {apiVersion: null})

/**
 * Gets the school users for a given school
 * @param params schoolId, onlyActive = true
 * @returns
 */
export async function getSchoolUsers(school_id: number, only_active = true) {
  try {
    const school_users = await userSchool.getUserSchoolsWithProfile(school_id)
    const users_ids = school_users.map((user:any) => user.user_id)
    const auth_users = await auth0.getUsers(users_ids) as any
    let users = school_users.map((s_user: any) => {
      const auth_user = auth_users.find((au:any) => au.user_id == s_user.user_id)
      return { ...auth_user, authUser: auth_user, scholaUser: s_user }
    })
    if(only_active) {
      users = users.filter((u: any) => u.scholaUser.status == 'Active')
    }
    return { users }
  } catch (error) {
    logger.error('getSchoolUsersError:', error)
    throw serverError
  }
}

/**
 * Gets the school information for the search profile
 * @param school_id
 * @param user_id
 * @returns Promise<unknown>
 */
export async function getSchool(school_id: number, user_id?: string) {
  try {
    if(isNaN(Number(school_id))) throw notFoundError
    // Get school for search profile

    // Get school information
    let db_result = await postgres.query(queries.getSchool(), [school_id])
    const school = db_result.rows[0]
    if(!school) throw notFoundError
    if (school.logo_image) {
      school.logo_image = `${S3_DOMAIN}/${school.logo_image}`
    }
    // Get school images for school
    const db_images = postgres.query(queries.getSchoolImages(), [school_id])
    // Get school features and groups
    const db_features = postgres.query(queries.getSchoolFeatures(), [school_id])
    // Get school videos for school
    const db_videos = postgres.query(queries.getSchoolVideos(), [school_id])
    // Get school events
    const db_events = postgres.query(events_queries.getSchoolEvents(), [school_id])
    // Get school scholaboosts
    const db_boosts = postgres.query(queries.getScholaBoosts(), [school_id])
    // Get endorsement summary
    const db_endorsement = postgres.query(endorsement_queries.getSchoolEndorsementSummary(), [school_id])
    // Get user click count
    const db_user = postgres.query(queries.getSchoolUserClickCount(), [school_id])
    // Get average parent satisfaction
    const db_parent_satisfaction = postgres.query(queries.getSchoolAverageParentSatisfaction(), [school_id])
    // Get average student happiness
    const db_student_happiness = postgres.query(queries.getAverageStudentHappiness(), [school_id])
    // Get school phones
    const db_phones = postgres.query(queries.getSchoolPhones(), [school_id])
    // Get school fast facts
    const { fast_facts } = await getFastFacts(school_id)

    // TODO: if we use the data on the UI on the future will get the features from the db instead of ES (previous code)
    const features_rating = 0

    // Get all additional info
    const db_images_response = await db_images
    const db_features_response = await db_features
    const db_videos_response = await db_videos
    const db_events_response = await db_events
    const db_boosts_response = await db_boosts
    const db_endorsement_response = await db_endorsement
    const db_user_response = await db_user
    const db_parent_satisfaction_response = await db_parent_satisfaction
    const db_student_happiness_response = await db_student_happiness
    const db_phones_response = await db_phones
    const images = db_images_response.rows
    const features = db_features_response.rows
    const videos = db_videos_response.rows
    const events = db_events_response.rows
    const scholaboosts = db_boosts_response.rows
    const endorsement_summary = db_endorsement_response.rows
    const user_click_count = Number(db_user_response.rows[0].count)
    const average_parent_satisfaction = db_parent_satisfaction_response.rows[0].avg
    const average_student_happiness = db_student_happiness_response.rows[0].avg
    const phone = db_phones_response.rows[0] // We get the phone in the first element
    // Get claimed school information
    const user_school = await userSchool.getBySchoolId(school_id)
    const claimed = user_school.length > 0 ? true : false
    // Need fill recruiterPro form
    const recruiter_pro = await needFillRecruiterProForm(school)

    const result = {
      ...school,
      images,
      videos,
      features,
      events,
      scholaboosts,
      claimed,
      endorsements: endorsement_summary,
      user_clicks: user_click_count,
      parent_satisfaction: _.round(average_parent_satisfaction, 1),
      children_happiness: _.round(average_student_happiness, 1),
      ...recruiter_pro,
      features_rating,
      twilio_phone: phone?.number || '',
      has_premium_access: ((school.plan_id && school.plan_id !== '' && school.plan_id !== null && school.plan_id.toLowerCase() !== 'scholabasics') && moment(school.subscription_end).isAfter()),
      fast_facts
    }

    // Validate if it the request was made by an authenticated user
    if(user_id) {
      const endorsed = await endorsementController.isEndorsedByUser({school_id, user_id})
      result.endorsed_by_user = endorsed
      // Get header image
      db_result = await postgres.query(queries.getHeaderImage(), [school_id])
      const header_image = db_result.rows[0]
      if(header_image) {
        result.headerImage = `${S3_DOMAIN}/${header_image.filename}`
      }
    }
    return result
  } catch (error) {
    logger.error('getSchoolError:', school_id, error.message)
    if(error.statusCode === notFoundError.statusCode) throw error
    throw serverError
  }
}

/**
 * Gets an array of schools paginated
 * @param pagination_options
 * @param user_id
 * @param scope
 * @param name
 * @param state
 * @returns Promise<unknown>
 */
export async function getSchools( user_id: string, params: GetSchoolsQuery, scope: Array<string | number> ) {
  await validate(params, schemas.get_schools)
  const query_options: QueryOptions = {
    state: params.state,
    user_id: params.user_id
  }
  if( (scope.indexOf('superadmin')) !== -1 || (scope.indexOf('scholaoffshore') !== -1) ) {
    query_options.is_super_admin = true
  } else {
    const school_ids = await userSchool.getSchoolIds(user_id)
    query_options.whitelist = school_ids
  }
  const page = params.page || 1
  const page_size = params.pageSize || 10
  const {schools, row_count} = await getAllMatchingName({
    page,
    pageSize: page_size
  }, query_options, params.name)
  return {
    pagination: {
      page,
      pageSize: page_size,
      rowCount: row_count,
      pageCount: Math.ceil(row_count / page_size)
    },
    results: schools
  }
}

export async function getSchoolsAutoComplete( name?: string, state?: string ) {
  const query_options: QueryOptions = {
    state
  }
  const page = 1
  const page_size = 10
  const {schools, row_count} = await getAllMatchingName({
    page,
    pageSize: page_size,
  }, query_options, name)
  // return {results: schools}
  return {
    pagination: {
      page,
      pageSize: page_size,
      rowCount: row_count,
      pageCount: Math.ceil(row_count / page_size)
    },
    results: schools
  }
}

/**
 * Renamed from getSchoolsAutoComplete2
 * @param {string} [name]
 * @param {string} [state]
 * @returns
 */
export async function getSchoolsElasticSearch( name?: string, state?: string ) {
  const query_options: QueryOptions = {
    state
  }
  const pagination = {
    page: 1,
    pageSize: 10
  }
  try {
    if(name) {
      return await searchSchoolsSimple(name, pagination)
    } else {
      const {schools, row_count} = await getAllMatchingName(pagination, query_options)
      return {
        pagination: {
          page: pagination.page,
          pageSize: pagination.pageSize,
          rowCount: row_count,
          pageCount: Math.ceil(row_count / pagination.pageSize)
        },
        results: schools
      }
    }
  } catch (error) {
    logger.error('getSchoolsElasticSearchError:', error)
    throw serverError
  }
}

export async function getFeaturesWithFinalScale( school_id: number ) {
  const school = await schoolGrades(school_id)
  if(!school) throw notFoundError
  const es_school = await search.getSchoolById(school_id.toString())
  if(!es_school) throw notFoundError
  try {
    // Get an array of grades the school provides
    let school_grades: Array<string> = []
    if(school.grades) {
      school.grades.split(',').forEach( (grade: string) => {
        if(grade) school_grades.push(grade)
      })
    } else {
      school_grades = GRADES.slice(
        GRADES.indexOf(school.min_grade),
        GRADES.indexOf(school.max_grade) + 1,
      )
    }
    // Get groups for those grades
    const groups = await getGroupsByGrades(school_grades)
    // Get features with final scale
    const features_weightage = _.get(es_school, ['_source', 'features_weightage'])
    _.forEach(groups, group => {
      _.forEach(group.features, feature => {
        const fw = _.find(features_weightage, (fw) => fw.feature_id === feature.id)
        feature.final_scale = fw ? _.round(fw.final_scale, 1) : null
      })
    })
    return groups
  } catch (error) {
    logger.error('getFeaturesWithFinalScaleError:', error)
    throw serverError
  }
}

export async function getSchoolTeacherStaff( school_id: number ) {
  const school = await getSchoolById(school_id)
  if(!school) throw notFoundError
  try {
    const db_response = await postgres.query(queries.getSchoolTeacherStaff(), [school_id])
    return db_response.rows[0]
  } catch (error) {
    logger.error('getSchoolTeacherStaffError:', error)
    throw serverError
  }
}

export async function getFAQSfromSchool(schoolId: number) {
  const school = await getSchoolById(schoolId)
  if(!school) throw notFoundError

  try {
    const db_response = await postgres.query(queries.getSchoolFAQs(), [schoolId])
    const db_response_count = await postgres.query(queries.countPublishedQuestions(), [schoolId])

    const publishedCount = Number(db_response_count.rows[0].count)

    return {
      published: publishedCount,
      questions: db_response.rows,
    }
  } catch (error) {
    logger.error('getFAQSfromSchool:', error)
    throw serverError
  }
}

export async function getFAQsPublished(schoolId: number) {
  const school = await getSchoolById(schoolId)
  if(!school) throw notFoundError

  try {
    const db_response = await postgres.query(queries.getFAQsPublished(), [schoolId])
    return db_response.rows
  } catch (error) {
    logger.error('getFAQsPublished:', error)
    throw serverError
  }
}

export async function getFAQuestion(schoolId: number, questionId: number) {
  try {
    const db_response = await postgres.query(queries.getQuestion(), [schoolId, questionId])

    if (db_response.rows.length > 0) {
      return db_response.rows[0]
    } else {
      throw notFoundError
    }

  } catch (error) {
    logger.error('getFAQuestion:', error)
  }
}

export async function postQuestion({ schoolId, question, answer, isPublished }: FAQschool) {
  try {
    const db_response = await postgres.query(queries.postQuestion(), [schoolId, question, answer, isPublished])
    return db_response.rows

  } catch (error) {
    logger.error('postQuestion:', error)
    throw serverError
  }

}

export async function patchQuestion(schoolId: number, questionId: number, fields: Question) {
  const foundQuestion = await getFAQuestion(schoolId, questionId)
  if(!foundQuestion) throw notFoundError

  try {
    const values = Object.values(fields)
    const updatedQuestion = await postgres.query(queries.patchQuestion(fields), [...values, questionId])
    return updatedQuestion.rows

  } catch (error) {
    logger.error('patchQuestion:', error)
  }
}

/**
 * School facilities CRUD functions
*/
export async function getFacilities(schoolId: number) {
  const school = await getSchoolById(schoolId)
  if(!school) throw notFoundError

  try {
    const db_response = await postgres.query(queries.getFacilities(), [schoolId])

    return db_response.rows
  } catch (error) {
    logger.error('getFacilities:', error)
    throw serverError
  }
}

export async function addFacility({ school_id, name, description, params }: NewFacility) {
  try {
    const subfolder = `school/${school_id}`

    const insert_params: FacilitySchool = {
      school_id: school_id,
      name,
      description,
    }

    if (params.original && params.original.length) {
      const renamed_file = renameFile(params.file[0].originalname)

      const  upload_image_result = await uploadLogo({
        original: params.original[0].buffer,
        subfolder,
        filename: renamed_file,
        alt_img: params.file && params.file.length ? params.file[0].buffer : undefined,
      }, {
        url: `https://${S3_BUCKET}.s3.amazonaws.com/school/${school_id}}`,
        disable_random: true,
        is_school_image: true,
        is_profile_image: false
      })

      insert_params.image_filename = `${subfolder}/${upload_image_result.key}`
    }

    const values = await getObjectValues(insert_params)
    const db_response = await postgres.query(insertQuery('school_facilities', insert_params), values)

    return {
      ...db_response.rows[0],
      filename: db_response.rows[0].image_filename ? `//${S3_BUCKET}.s3.amazonaws.com/${subfolder}/${db_response.rows[0].image_filename}` : ''
    }

  } catch (error) {
    logger.error('addFacility:', error)
    throw serverError
  }
}

export async function updateFacility(school_id: number, facility_id: number, facility: FacilitySchool, params: ParamsImage) {
  try {

    const subfolder = `school/${school_id}`

    if (params.original && params.original.length) {
      const renamed_file = renameFile(params.file[0].originalname)

      const  upload_image_result = await uploadLogo({
        original: params.original[0].buffer,
        subfolder,
        filename: renamed_file,
        alt_img: params.file && params.file.length ? params.file[0].buffer : undefined,
      }, {
        url: `https://${S3_BUCKET}.s3.amazonaws.com/school/${school_id}}`,
        disable_random: true,
        is_school_image: true,
        is_profile_image: false
      })

      facility.image_filename = `${subfolder}/${upload_image_result.key}`
    }

    const values = Object.values(facility)
    const updatedFacility = await postgres.query(queries.updateFacility(facility), [...values, facility_id])
    return updatedFacility.rows[0]

  } catch (error) {
    logger.error('updateFacility:', error)
    throw serverError
  }
}

export async function deleteFacility(schoolId: number, facilityId: number) {
  try {
    const db_response = await postgres.query(queries.deleteFacility(), [schoolId, facilityId])
    return db_response.rows
  } catch (error) {
    logger.error('deleteFacility:', error)
    throw serverError
  }
}

export async function getSchoolDemographics( school_id: number ) {
  const school = await schoolGrades(school_id)
  if(!school) throw notFoundError
  try {
    const school_demo = postgres.query(queries.getSchoolDemographics(), [school_id])
    const teacher_staff = postgres.query(queries.getSchoolTeacherStaff(), [school_id])

    const db_teacher_staff = await teacher_staff
    const db_school_demo = await school_demo

    const school_demographics = db_school_demo.rows

    let sd_breakdown = []
    if(db_school_demo.rowCount > 0) {
      const school_demo_breakdown = await postgres.query(queries.getSchoolDemographicsBreakdown(), [school_demographics[0].id])
      sd_breakdown = school_demo_breakdown.rows

    }

    return {
      schoolDemo: db_school_demo.rows,
      sd_breakdown: sd_breakdown,
      school_teachers_staff: db_teacher_staff.rows,
    }
  } catch (error) {
    logger.error('getSchoolDemographicsError:', error)
    if(error.statusCode === notFoundError.statusCode) throw error
    throw serverError
  }
}

export async function createSchool(params: PostSchool, added_by?: string) {
  await validate(params, schemas.create_school)
  const school = await schoolIsDuplicated(params)
  if(school) throw conflictError
  // Create new school
  params.description = `${params.name} is a ${params.type} school in ${params.city} that ` +
  `serves grades ${params.grades}`
  const client = await transactional_client.connect()
  try {
    const now = moment().toDate()
    await client.query('BEGIN')
    const values = await getObjectValues(params)
    const db_response = await client.query(insertQuery('schools', params), values)
    const new_school = db_response.rows[0]
    // Insert log change is not part of the transaction
    await insertLogChange({
      table_name: 'users_schools',
      user_id: added_by,
      school_id: new_school.id,
      operation_type: 'add',
      new_values: new_school,
      created_at: now,
    })
    if(new_school.type === 'public') {
      // Update special education features
      await updateSpedFeatures(new_school.id)
    }
    // Index school on elastic search
    search.indexSchool(new_school)
    await client.query('COMMIT')
    return new_school
  } catch (error) {
    await client.query('ROLLBACK')
    logger.error('createSchoolError:', error)
    throw serverError
  } finally {
    client.release()
  }
}

export async function updateSchool(params: PatchSchool, school_id: number, updated_by: string, scope: Array<string | number>) {
  // if(scope.includes('system')) {
  //   await validate(params, schemas.close_school_admin)
  // } else if (scope.includes('superadmin')) {
  //   await validate(params, schemas.update_school_superadmin)
  // } else {
  //   await validate(params, schemas.update_school_admin)
  // }
  const old_school = await getSchoolById(school_id)
  if(!old_school) throw notFoundError
  const client = await transactional_client.connect()
  const now = moment().toDate()
  if(typeof params.deleted === 'boolean') {
    if(params.deleted) {
      params.deleted_at = now
      params.deleted_by = updated_by
    } else {
      params.deleted_at = null
      params.deleted_by = null
    }
  }
  try {
    await client.query('BEGIN')
    if(params.videos) {
      // Update school videos
      await updateSchoolVideos(school_id, params.videos, client)
      delete params.videos
    }
    // Format description in case of base64
    if(params.description){
      params.description = await schoolUtils.processSchoolDescription(params.description, school_id )
    }
    //events
    const events = params.events
    delete params.events
    //schoolTeacherStuff
    const school_teacher_stuff = params.schoolTeacherStuff
    delete params.schoolTeacherStuff

    //postgres has a native array type which uses a syntax incompatible with jsonb
    if(params.awards && Array.isArray(params.awards)){
      params.awards =  JSON.stringify(params.awards)
    }

    const condition = {id: school_id}
    const built_update = buildUpdate('schools', condition, {
      ...params,
      updated_at: now
    })
    const db_response = await client.query(built_update.text, built_update.values)
    const updated_school = db_response.rows[0]
    if(updated_school.type === 'public') {
      // Update special education features
      await updateSpedFeatures(updated_school.id)
    }
    // Insert log change is not part of the transaction
    await insertLogChange({
      table_name: 'schools',
      user_id: updated_by,
      school_id: updated_school.id,
      operation_type: 'edit',
      new_values: updated_school,
      old_values: old_school,
      created_at: now,
      updated_at: now,
    })
    if(old_school.deleted === false && params.deleted === true) {
      // Remove school from elastic search
      await search.removeSchool(updated_school.id)
    } else if(old_school.deleted === true && params.deleted === false) {
      // Index school on elastic search
      await search.indexSchool(updated_school)
    } else if(old_school.deleted !== true && params.deleted !== true) {
      // Update school on elastic search
      await search.updateSchool(updated_school, true)
    }
    await client.query('COMMIT')
    // TODO replace on hubspot?
    //update SF if the school is permanently closed
    // if(params.permanently_closed !== undefined && old_school.permanently_closed !== params.permanently_closed){
    //   const status = await salesforceController.getStatusBySchoolId(school_id)
    //   if (status){
    //     logger.debug('School on SF:', status)
    //     if(status.School_Permanently_Closed__c !== params.permanently_closed){
    //       if (params.permanently_closed){
    //         const source_close=(updated_school.review_business_status_source ? updated_school.review_business_status_source : 'platform')
    //         const date_close=(updated_school.review_business_status_date ? updated_school.review_business_status_date : moment() )
    //         await salesforceController.closeAccount(status.Id, source_close , date_close)
    //       } else {
    //         await salesforceController.openAccount(status.Id)
    //       }
    //     } else {
    //       logger.info('No status update on SF')
    //     }
    //   } else {
    //     logger.warn('School not found on SF: ', old_school.id)
    //   }
    // }


    //events
    await addOrUpdateEvents(school_id, events)
    //school_teacher_stuff
    await addOrUpdateSchoolTeacherStuff(school_id, school_teacher_stuff)

    const updatedSchool=await getSchool(school_id)

    if(params.plan_id && old_school.plan_id !== params.plan_id && (params.plan_id!=='' && params.plan_id.toLowerCase()!=='scholabasics')){
      //maybe require twilio phone create task on clickup
      const db_phones = postgres.query(queries.getSchoolPhones(), [school_id])
      if((await db_phones).rowCount===0){
        createSupportTicket(`Phone Number - ${updatedSchool.name}`,`${updatedSchool.name} needs a Twilio Phone number\nSchool Id: ${updatedSchool.id}\nPhone: ${updatedSchool.phone}`,3  )
      }

    }
    return updatedSchool
  } catch (error) {
    await client.query('ROLLBACK')
    logger.error('updateSchoolError:', error)
    throw serverError
  } finally {
    client.release()
  }
}

export async function updateSchoolData(school_id: number, phone?: string) {
  const old_school = await getSchoolById(school_id)
  if(!old_school) throw notFoundError
  const client = await transactional_client.connect()
  try {
    await client.query('BEGIN')
    const condition = {id: school_id}
    const built_update = buildUpdate('schools', condition, {phone})
    const db_response = await postgres.query(built_update.text, built_update.values)
    const updated_school = db_response.rows[0]
    const search_update = await search.updateSchool(updated_school)
    await client.query('COMMIT')
    return search_update
  } catch (error) {
    await client.query('ROLLBACK')
    logger.error('updateSchoolDataError:', error)
    throw serverError
  } finally {
    client.release()
  }
}

export async function updateSchoolLogo(params: UploadSchoolLogo, school_id: number, scope: Array<string | number>) {
  await validate(params, schemas.upload_school_logo)
  const school = await getSchoolById(school_id)
  if(!school) throw notFoundError
  try {
    const subfolder = `school/${school_id}`
    // If the school already has a logo, delete it from s3
    if(school.logo_image) {
      await deleteFile(`${subfolder}/${school.logo_image}`)
    }
    const key = await s3Upload({
      subfolder,
      filename: params.file_name,
      file_stream: params.buffer
    })
    const updated_school = await updateSchool({
      logo_image: key
    }, school_id, params.user_data.id, scope)
    return updated_school
  } catch (error) {
    logger.error('updateSchoolLogoError:', error)
    throw serverError
  }
}

export async function updateSchoolLogoV2(params: UpdateLogo, school_id: number, scope: Array<string | number>) {
  await validate(params, schemas.upload_school_logo_v2)
  const school = await getSchoolById(school_id)
  if(!school) throw notFoundError
  try {
    const subfolder = `school/${school_id}`
    // If the school already has a logo, delete it from s3
    if(school.logo_image) {
      await deleteFile(`${subfolder}/${school.logo_image}`)
    }
    const renamed_file = renameFile(params.file_name)
    const upload_logo_result = await uploadLogo({
      original: params.original,
      subfolder: subfolder,
      filename: renamed_file,
      alt_img: params.file,
    }, {
      url: `https://${S3_BUCKET}.s3.amazonaws.com/school/${school_id}}`,
      disable_random: true
    })
    const updated_school = await updateSchool({
      logo_image: `${subfolder}/${upload_logo_result.key}`
    }, school_id, params.user_data.id, scope)
    return {
      ...updated_school,
      logo_image: `https://${S3_BUCKET}.s3.amazonaws.com/${subfolder}/${upload_logo_result.key}`
    }
  } catch (error) {
    logger.error('updateSchoolLogoV2Error:', error)
    throw serverError
  }
}

export async function updateSchoolFeatures(params: UpdateSchoolFeatures, school_id: number) {
  await validate(params, schemas.update_school_features)
  try {
    await featureSchoolUtils.setFeaturesToSchool({
      school_id,
      updated_by: params.user_data.id,
      includes: params.included || [],
      excludes: params.excluded || [],
      tops: params.isTop || [],
      weightages: params.feature_weightage || [],
    })
    const school = await getSchool(school_id)
    await search.updateSchool(school)
    return school
  } catch (error) {
    logger.error('updateSchoolFeaturesError('+school_id+'):', error)
    throw serverError
  }
}

export async function resendCreateActivatedUser(params: ResendUserInSchool, school_id: number) {
  await validate(params, schemas.resend_emails_user)
  const school = await getSchoolById(school_id)
  if(!school) throw notFoundError
  await createActivatedUser(params.email, school.name, true, false, params.user_id)
}

export async function addSchoolUser(params: AddUserInSchool, school_id: number) {
  await validate(params, schemas.add_user_in_school)
  const school = await getSchoolById(school_id)
  if(!school) throw notFoundError
  const request_user = params.user_data.id
  try {
    const user = await auth0.getUserByEmail(params.email)
    let auth_user = user
    if(!user) {
      // Create user if email doesn't exist in auth provider
      const result = await createActivatedUser(params.email, school.name, false)
      auth_user = result.user
      await sendUserCreatedEmailMerged(params.email, school.name, school.id, result.ticket.link)
    } else {
      // auth_user = await auth0.getUser(user_id)
      await sendUserCreatedEmail(params.email, school.name, school.id)
    }
    const { user_id } = auth_user
    // Verify if user is a member of the school
    const is_school_user = await userSchool.isSchoolUser(school_id, user_id)
    const user_school_params = {
      school_id,
      user_id,
      user_school_deparment_id: params.deparment
    }
    let school_user
    if(is_school_user) {
      school_user = await userSchool.updateUserSchool(user_school_params,  request_user)
    } else {
      school_user = await userSchool.addUserSchool(user_school_params,  request_user)
    }
    const full_school = await getSchool(school_id)
    await search.updateSchool(full_school)
    // const auth_user = await auth0.getUser(user_id)

    // TODO replace on hubspot?
    // await salesforceController.schoolContactHandler({
    //   school: full_school,
    //   email: auth_user.email,
    //   firstName: auth_user.given_name,
    //   lastName: auth_user.family_name,
    //   phone: auth_user.phone_number,
    // })
    await createUpdateUserProfile({
      title: params.title,
      email: params.email,
      first_name: params.firstName,
      last_name: params.lastName,
      created_at: school_user.created_at,
      updated_at: school_user.updated_at,
      user_id: user_id
    }, request_user)
    return school_user
  } catch (error) {
    logger.error('addSchooluserError:', error)
    throw serverError
  }
}

export async function removeSchoolUser(school_id: number, user_id: string, deleted_by: string) {
  // Verify if user is a member of the school
  const is_school_user = await userSchool.isSchoolUser(school_id, user_id)
  if(!is_school_user) throw notFoundError
  if(deleted_by === user_id) throw badRequestError
  const user_school = await userSchool.removeUserSchool(school_id, user_id, deleted_by)
  const school = await getSchool(school_id)
  await search.updateSchool(school)
  return user_school
}

export async function addSchoolImage(params: AddSchoolImages, school_id: number) {
  await validate(params, schemas.add_school_image)
  const school = await getSchoolById(school_id)
  if(!school) throw notFoundError
  try {
    const subfolder = `school/${school_id}`
    const renamed_file = renameFile(params.file_name)
    const  upload_logo_result = await uploadLogo({
      original: params.original,
      subfolder: subfolder,
      filename: renamed_file,
      alt_img: (params.file || params.original),
    }, {
      url: `https://${S3_BUCKET}.s3.amazonaws.com/school/${school_id}}`,
      disable_random: true,
      is_school_image: true,
      is_profile_image: params.is_profile_image,
    })
    const now = moment.utc()
    const insert_params = {
      school_id,
      filename: `${subfolder}/${upload_logo_result.key}`,
      profile_image: params.is_profile_image,
      created_at: now,
      updated_at: now
    }
    const values = await getObjectValues(insert_params)
    const db_response = await postgres.query(insertQuery('school_images', insert_params), values)
    return {
      ...db_response.rows[0],
      profile_image: String(db_response.rows[0].profile_image), // Cast the booelan to string to match previous API response
      filename: `//${S3_BUCKET}.s3.amazonaws.com/${subfolder}/${upload_logo_result.key}`
    }
  } catch (error) {
    logger.error('addSchoolImageError:', error)
    throw serverError
  }
}

export async function addSchoolCustomApplication (params: AddSchoolCustomApplication, scope: Array<string | number>) {
  await validate(params, schemas.add_custom_application)
  const school = await getSchoolById(params.school_id)
  if(!school) throw notFoundError
  try {
    const key = await s3Upload({
      subfolder: `school/${params.school_id}`,
      filename: params.file_name,
      file_stream: params.application_file,
      disable_random: false
    })
    const updated_school = await exports.updateSchool({
      application_pdf_url: key,
      is_custom_application: true
    }, params.school_id, params.user_data?.id, scope)
    await sendSchoolCustomApplicationEmail(school.name, params.school_id, key)
    return updated_school
  } catch (error) {
    logger.error('addSchoolCustomApplicationError:', error)
    throw serverError
  }
}

export async function removeSchoolImage(school_id: number, image_id: number) {
  const school_image = await getSchoolImage(school_id, image_id)
  if(!school_image) throw notFoundError
  try {
    const subfolder = `school/${school_id}`
    const filename = school_image.filename.split(`${subfolder}/`)[1]
    let format_name = ''
    if (filename) {
      format_name = filename
      if(filename.indexOf('l-')  > -1 || filename.indexOf('m-')  > -1 || filename.indexOf('s-')  > -1) {
        format_name = filename.substr(2)
      } else if( filename.indexOf('original-')  > -1) {
        format_name = filename.substr(9)
      }
    } else {
      format_name = school_image.filename.split('/').pop()
    }
    await removeSchoolImagesDB(format_name, school_id, subfolder, image_id)
    const school = await getSchool(school_id)
    await search.updateSchool(school)
    return school
  } catch (error) {
    logger.error('removeSchoolImageError:', error)
    throw serverError
  }
}

export async function updateSchoolSelectedHeader(school_id: number, image_id: number) {
  const db_result = await postgres.query(queries.getHeaderImage(), [school_id])
  const header_image = db_result.rows[0]
  const school = await getSchoolById(school_id)
  if(!school) throw notFoundError
  try {
    await postgres.query(queries.updateSchoolHeader(), [school_id, image_id, header_image?.id ?? null])
    const schoolImages = await postgres.query(queries.getAllSchoolImages(), [school_id])
    return schoolImages.rows
  } catch (error) {
    logger.error('updateSchoolSelectedHeaderError:', error)
    throw serverError
  }
}

export async function claimSchool(params: SchoolClaim) {
  await validate(params, schemas.claim_school, true)

  try {
    const value = await lookupPhone(params.phone)
    if(!value.valid || value.validationErrors?.length || !value){
      return {emailValidation: false, validationErrors: [{key: 'phone', type:'validation', message: 'Invalid Phone Number'}]}
    }
    if(!params.school.id) {
      await sendSchoolClaimEmail(params, '(Manual)')
      return {emailValidation: false} // manual claim of possible new school
    } else {
      // TODO replace on hubspot?
      // await salesforceController.claimSchool(params)
      const {users} = await getSchoolUsers(params.school.id)
      if(users.length > 0) {
        await sendSchoolClaimEmail(params, '(School Already Claimed 🤔)')
        return { claimed: true, emailValidation: false}
      }
      if(!checkEmailDomain(params.email)) {
        // add user to school (automatic claim)
        await addUserToSchool(params, '')
        await sendSchoolClaimEmail(params, '(Automatic)')
        return {emailValidation: true}
      }
    }
    await sendSchoolClaimEmail(params, '(Manual)')
    return {emailValidation: false}
  } catch (error) {
    logger.error('claimSchoolError:', error)
    throw serverError
  }
}

export async function automateClaimSchool(params: SchoolClaim, code: string, added_by: string) {
  await validate(params, schemas.claim_school)
  const claim_code = await verifyClaimCode(params.school.id, code)
  if(!claim_code) throw badRequestError
  const today = Number(new Date())
  const created_at = Number(new Date(claim_code.created_at))
  let diff = Math.abs(today - created_at)
  diff = Math.floor((diff / 100) / 60)
  if(diff > CLAIM_CODE_LIMIT_TIME) throw notFoundError
  try {
    const school_users = await getSchoolUsers(params.school.id)
    if(school_users.users.length > 0) return { claimed: true, status: false}

    return await addUserToSchool(params, added_by)
  } catch (error) {
    logger.error('automateClaimSchoolError:', error)
    throw serverError
  }
}

async function addUserToSchool(params: SchoolClaim, added_by: string) {
  try {
    let user
    const school_user = await auth0.getUserByEmail(params.email)
    if(!school_user?.user_id) {
      const result = await createActivatedUser(params.email, params.school.name, false)
      user = result.user
      await sendUserCreatedEmailMerged(params.email, params.school.name, params.school.id, result.ticket.link)
    } else {
      user = await auth0.getUser(school_user.user_id)
    }
    const is_school_user = await userSchool.isSchoolUser(params.school.id, user.user_id)
    let new_school_user
    if(!is_school_user) {
      new_school_user = await userSchool.addUserSchool({
        school_id: params.school.id,
        user_id: user.user_id
      }, added_by)
    }
    //const school = await getSchool(params.school.id)

    // TODO replace on hubspot?
    // await salesforceController.schoolContactHandler({
    //   school,
    //   email: user.email,
    //   firstName: params.firstName,
    //   lastName: params.lastName,
    //   phone: params.phone,
    //   jobTitle: params.jobTitle
    // })
    await createUpdateUserProfile({
      phone: params.phone,
      ext: params.ext,
      email: params.email,
      title: params.jobTitle,
      first_name: params.firstName,
      last_name: params.lastName,
      user_id: user.user_id
    }, added_by)
    const result = { status: true}
    if(new_school_user) {
      Object.assign(result, {schoolUser: new_school_user})
    }
    return result
  } catch (error) {
    logger.error('addUserToSchool:', error)
    throw serverError
  }

}

export async function exportData() {
  try {
    const db_response = await postgres.query(queries.exportData())
    const school_data = db_response.rows
    const csv_stream = csv_stringify_sync(school_data, {columns: SF_SCHOOL_EXPORT_COLUMNS, header: true })
    return csv_stream
  } catch (error) {
    logger.error('exportDataError:', error)
    throw serverError
  }
}

export async function exportStatistics() {
  try {
    const db_response = await postgres.query(queries.exportStatistics())
    const statistics_data = db_response.rows
    const csv_stream = csv_stringify_sync(statistics_data, {columns: STATISTICS_EXPORT_COLUMNS, header: true })
    return csv_stream
  } catch (error) {
    logger.error('exportStatisticsError:', error)
    throw serverError
  }
}

export async function exportTours() {
  try {
    const db_response = await postgres.query(queries.exportTours())
    const tours_data = db_response.rows
    const csv_stream = csv_stringify_sync(tours_data, {columns: TOURS_EXPORT_COLUMNS, header: true })
    return csv_stream
  } catch (error) {
    logger.error('exportToursError:', error)
    throw serverError
  }
}

export async function exportLeads() {
  try {
    const db_response = await postgres.query(queries.exportLeads())
    const leads_data = db_response.rows
    const csv_stream = csv_stringify_sync(leads_data, {columns: LEADS_EXPORT_COLUMNS, header: true })
    return csv_stream
  } catch (error) {
    logger.error('exportLeadsError:', error)
    throw serverError
  }
}

export async function exportConnections() {
  try {
    const db_response = await postgres.query(queries.exportConnections())
    const connections_data = db_response.rows
    const csv_stream = csv_stringify_sync(connections_data, {columns: CONNECTIONS_EXPORT_COLUMNS, header: true })
    return csv_stream
  } catch (error) {
    logger.error('exportConnectionsError:', error)
    throw serverError
  }
}

export async function bulletinBoard(params: LocationAndPagination) {
  await validate(params, schemas.location_and_pagination)
  const {pagination, data} = await getDataAndPagination(params, queries.bulletinBoardCount(), queries.bulletinBoard(params.lat, params.lon), {
    school_url: true
  })
  return {
    pagination,
    results: data
  }
}

export async function topFeatured(params: LocationAndPagination) {
  await validate(params, schemas.location_and_pagination)
  const {pagination, data} = await getDataAndPagination(params, queries.topFeaturedCount(), queries.topFeatured(params.lat, params.lon))
  return {
    pagination,
    results: data
  }
}

export async function topNearby(params: LocationAndPagination) {
  await validate(params, schemas.location_and_pagination)
  const {pagination, data} = await getDataAndPagination(params, queries.topNearbyCount(), queries.topNearby(params.lat, params.lon), {
    lat_lon_required: true
  })
  return {
    pagination,
    results: data
  }
}

export async function topMostPopular(params: LocationAndPagination) {
  await validate(params, schemas.location_and_pagination)
  const {pagination, data} = await getDataAndPagination(params, queries.topMostPopularCount(), queries.topMostPopular(params), {
    address_required: true
  })
  return {
    pagination,
    results: data
  }
}

export async function updateSchoolProfilePicture(school_id: number, image_id: number) {
  await updateProfilePicture(school_id, image_id)
  const school = await getSchool(school_id)
  await search.updateSchool(school)
  return school
}

export async function addSchoolScheduleTourV2(params: ScheduleTour) {
  await validate(params, schemas.schedule_tour)
  const school = await getSchoolById(params.school_id)
  if(!school) throw notFoundError
  try {
    const now = moment().toDate()
    const schedule_tours = params.students.map( (obj: Student) => ({
      school_id: params.school_id,
      full_name: `${params.parent_first_name} ${params.parent_last_name}`,
      email: params.email,
      phone: params.phone,
      tour_status: 'active',
      tour_date: params.tour_date,
      tour_time: params.tour_time,
      studen_name: `${obj.child_first_name} ${obj.child_last_name}`,
      grade: obj.grade,
      student_birthdate: obj.child_birthdate || null,
      user_id: params.user_id,
      referred_by: params.referred_by,
      referred_by_other: params.referred_by_other,
      preferred_contact: params.preferred_contact,
      language: params.language,
      year: params.year,
      zipcode: params.zipcode,
      created_at: now,
      updated_at: now,
    }))
    const batch_text = batchParametrizedInsertQuery('school_schedule_tour', schedule_tours)
    const db_response = await postgres.query(batch_text, getObjectValuesFromArray(schedule_tours))
    return db_response.rows
  } catch (error) {
    logger.error('addSchoolScheduleTourV2Error:', error)
    throw serverError
  }
}

export async function getSchoolScheduleTour(school_id: number, params: GetScheduleTour) {
  await validate(params, schemas.get_schedule_tour)
  const page_rows = params.pageSize || 10
  const page = params.page || 1
  const page_offset = (page - 1) * page_rows
  try {
    const db_response = await postgres.query(queries.getSchoolScheduleTour(params.includeArchived), [school_id, page_rows, page_offset])
    const total_response = await postgres.query(queries.getTotalSchoolScheduleTour(params.includeArchived), [school_id])
    const total = total_response.rowCount > 0 ? Number(total_response.rows[0].count) : 0
    return {
      pagination: {
        page,
        pageSize: page_rows,
        rowCount: total,
        pageCount: Math.ceil(total / page_rows)
      },
      results: db_response.rows
    }
  } catch (error) {
    logger.error('getSchoolScheduleTourError:', error)
    throw serverError
  }
}

export async function getSchoolScheduleTourById(school_id: number, tour_id: number) {
  const school = await getSchoolById(school_id)
  if(!school) throw notFoundError
  try {
    const db_response = await postgres.query(queries.getSchoolScheduleTourById(), [tour_id])
    if(db_response.rowCount === 0) throw notFoundError
    return db_response.rows[0]
  } catch (error) {
    logger.error('getSchoolScheduleTourByIdError:', error)
    if(error.statusCode === notFoundError.statusCode) throw notFoundError
    throw serverError
  }
}

export async function updateSchoolScheduleTour(school_id: number, tour_id: number, user_id: string, params: UpdateScheduleTour) {
  await validate(params, schemas.update_schedule_tour)
  const school = await getSchoolById(school_id)
  if(!school) throw notFoundError
  try {
    const condition = {id: tour_id, school_id}
    const update_params: UpdateScheduleTourDB = {}
    for (const [key, value] of Object.entries(params)) {
      update_params[snakeCase(key)] = value
    }
    if(Object.keys(update_params).length === 0) return {}
    const built_update = buildUpdate('school_schedule_tour', condition, update_params)
    const db_response = await postgres.query(built_update.text, built_update.values)
    const updated_record = db_response.rows[0]
    await createTourNote(user_id, params.tourStatus, tour_id)
    return updated_record
  } catch (error) {
    logger.error('updateSchoolScheduleTourError:', error)
    throw serverError
  }
}

export async function updateSchoolScheduleTourLead(school_id: number, tour_id: number, params: UpdateLeadTour) {
  await validate(params, schemas.update_lead_tour)
  const tour = await getSchoolScheduleTourById(school_id, tour_id)
  if(tour.lead_id) return {}
  const student = params.students[0]
  delete params.students
  const lead_params = {
    ...student,
    ...params,
    child_birthdate: student.child_birthdate ? moment(student.child_birthdate).format('YYYY-MM-DD HH:mm:ss') : null,
    created_at: moment(params.created_at).format('YYYY-MM-DD HH:mm:ss')
  } as any

  const created_lead = await leadsController.createLead(lead_params)
  await leadUtils.addLeadHistory(created_lead.id, 'status', 'new')
  await cloneNotes(tour_id, created_lead.id)
  try {
    const condition = {id: tour_id}
    const built_update = buildUpdate('school_schedule_tour', condition, {lead_id: created_lead.id})
    await postgres.query(built_update.text, built_update.values)
    const lead = await leadsController.getLead(created_lead.id, created_lead.school_id)
    return {
      ...created_lead,
      application_token: lead.application_token,
    }
  } catch (error) {
    logger.error('updateSchoolScheduleTourLeadError:', error)
    throw serverError
  }
}

export async function multiUpdateSchoolScheduleTour(school_id: number, user_id: string, params: ScheduleTourU) {
  await validate(params, schemas.schedule_tour_u)
  const update_promises = params.scheduleTours.map(async (obj) => {
    const {tourId, ...rest} = obj
    return await updateSchoolScheduleTour(school_id, tourId, user_id, rest)
  })
  return await Promise.all(update_promises)
}

export async function addSchoolTestimonial(school_id: number, params: SchoolTestimonial) {
  await validate(params, schemas.school_testimonial)
  const school = await getSchoolById(school_id)
  if(!school) throw notFoundError
  try {
    const insert_params = {
      school_id,
      user_id: params.userId,
      full_name: params.fullName,
      testimonial: params.testimonial,
      role: params.role,
      created_at: moment.utc()
    }
    const values = await getObjectValues(insert_params)
    const db_response = await postgres.query(insertQuery('school_testimonial', insert_params), values)
    return db_response.rows[0]
  } catch (error) {
    logger.error('addSchoolTestimonialError:', error)
    throw serverError
  }
}

export async function uploadSchoolTestimonial(school_id: number, testimonial_id: number, params: SchoolTestimonial) {
  await validate(params, schemas.school_testimonial)
  const school = await getSchoolById(school_id)
  if(!school) throw notFoundError
  try {
    const update_params = {
      full_name: params.fullName,
      role: params.role,
      testimonial: params.testimonial,
    }
    const condition = {id: testimonial_id}
    const built_update = buildUpdate('school_testimonial', condition, update_params)
    const db_response = await postgres.query(built_update.text, built_update.values)
    return db_response.rows[0]
  } catch (error) {
    logger.error('uploadSchoolTestimonialError:', error)
    throw serverError
  }
}

export async function removeSchoolTestimonial(school_id: number, testimonial_id: number) {
  const school = await getSchoolById(school_id)
  if(!school) throw notFoundError
  try {
    const db_response = await postgres.query(queries.deleteSchoolTestimonial(), [testimonial_id])
    return db_response.rows[0]
  } catch (error) {
    logger.error('removeSchoolTestimonialError:', error)
    throw serverError
  }
}

export async function getSchoolTestimonial(school_id: number) {
  try {
    const db_response = await postgres.query(queries.getSchoolTestimonialBySchool(), [school_id])
    // if(db_response.rowCount === 0) throw notFoundError
    return db_response.rows
  } catch (error) {
    logger.error('getSchoolTestimonialError:', error)
    if(error.statusCode === notFoundError.statusCode) throw notFoundError
    throw serverError
  }
}

export async function addSchoolSeats(school_id: number, params: AddSchoolSeats) {
  await validate(params, schemas.add_school_seats)
  const school = await getSchoolById(school_id)
  if(!school) throw notFoundError
  try {
    const insert_params = {
      school_id,
      ...params,
      created_at: moment.utc()
    }
    const values = await getObjectValues(insert_params)
    const db_response = await postgres.query(insertQuery('school_seat_availables', insert_params), values)
    return db_response.rows[0]
  } catch (error) {
    logger.error('addSchoolSeatsError:', error)
    throw serverError
  }
}

export async function updateSchoolSeats(school_id: number, params: UpdateSchoolSeats) {
  await validate(params, schemas.update_school_seats)
  const school = await getSchoolById(school_id)
  if(!school) throw notFoundError
  try {
    // Delete previous school seats record
    await postgres.query(queries.deleteSchoolSeats(), [school_id])
    // Insert new school seats from the array in the params
    const batch_insert = params.seatsAvailables.map( (element: AddSchoolSeats) => {
      element.availables = Number(element.availables)
      element.total = Number(element.total)
      element.block_asm = Boolean(element?.block_asm)
      return {
        school_id,
        grade: element.grade,
        availables: element.availables,
        total: element.total,
        block_asm: element.block_asm,
        created_at: moment.utc(),
      }
    })
    const batch_text = batchParametrizedInsertQuery('school_seat_availables', batch_insert)
    const db_response = await postgres.query(batch_text, getObjectValuesFromArray(batch_insert))
    await syncAboutYourSchoolEnrollment(school_id, params.seatsAvailables)
    await syncOnboardingEnrollments(school_id, params.seatsAvailables)
    return db_response.rows
  } catch (error) {
    logger.error('updateSchoolSeatsError:', error)
    throw serverError
  }
}

export async function getSchoolSeats(school_id: number) {
  try {
    const db_result = await postgres.query(queries.getSchoolSeats(), [school_id])
    // if(db_result.rowCount === 0) throw notFoundError
    return db_result.rows
  } catch (error) {
    logger.error('getSchoolSeatsError:', error)
    if(error.statusCode === notFoundError.statusCode) throw notFoundError
    throw serverError
  }
}

export async function getSchoolSeatsForGrade(school_id: number, grade: string) {
  try {
    const db_result = await postgres.query(queries.getSchoolSeatsForGrade(), [school_id, grade])
    return db_result.rows[0]
  } catch (error) {
    logger.error('getSchoolSeatsGradeError:', error)
    throw serverError
  }
}

export async function addSchoolTourTimes(school_id: number, params: AddSchoolTourTimes) {
  await validate(params, schemas.add_tour_times)
  const school = await getSchoolById(school_id)
  if(!school) throw notFoundError
  try {
    const now = moment().toDate()
    const insert_params = {
      school_id,
      week_availability: params.weekAvailability,
      morning_start: params.morningStart,
      morning_end: params.morningEnd,
      afternoon_start: params.afternoonStart || null,
      afternoon_end: params.afternoonEnd || null,
      created_at: now,
      updated_at: now,
    }
    const values = await getObjectValues(insert_params)
    const db_response = await postgres.query(insertQuery('school_tour_times', insert_params), values)
    return db_response.rows[0]
  } catch (error) {
    logger.error('addSchoolTourTimesError:', error)
    throw serverError
  }
}

export async function searchSchoolsV2(params: SearchSchoolV2, user_id?: string, ip?: string, grades_param?: any) {
  try{
    await validate(params, schemas.search_school_v2)
    const features = buildFeatures(params.f)
    const school_types =  params.school_types || []
    delete params.school_types
    const result = await searchSchools.v2(params, features)
    const result_schools = result.schools
    const search_result = result.search_result
    const grades = grades_param ? grades_param : params.grades
    if(result_schools.length > 0) {
    // Filter for save search history schools
      const schools:SchoolIds[] = []
      let added = false
      result_schools.forEach((school: SearchedSchool) => {
        if(grades?.length > 0) {
          if(grades && grades.length === 0 || grades.some((x: any) => x === 'all')) {
            added = true
          } else {
            if(school.grades?.length > 0) {
              if (school.grades.some(grade => grades.some((x: any) => x == grade)) === true) {
                added = true
              }
            }
          }
        }
        if(added) {
          if(school_types) {
            if(school_types.length === 0 || school_types.some(x => x === school.type)){
              schools.push({schoolId: school.id})
            }
          }
        }
      })
      if(!params.ignore && schools.length > 0) {
        const search_history = await saveSearchHistory(params, user_id || params.userId, ip)
        await saveSearchHistorySchools(search_history.id, schools)
      }
    }
    return {
      pagination: search.getPaginationData({
        page: params.page,
        pageSize: params.pageSize
      }, search_result),
      results: result_schools
    }
  } catch (error) {
    logger.error('searchSchoolsV2Error:', error)
    throw serverError
  }
}

export async function searchSchoolsV1(params: SearchSchool, user_id?: string, ip?: string) {
  await validate(params, schemas.search_school)
  try {
    const grades = _.join(params.grades, ',')
    const features = buildFeatures(params.f)
    const result = await searchSchools.v1(params, false, features)
    const result_schools = result.schools
    const search_result = result.search_result
    const schools:SchoolIds[] = []
    if(result_schools && result_schools.length > 0) {
      for (let i = 0; i < result_schools.length; i++) {
        const school = result_schools[i]
        schools.push({schoolId: school.id})
        if(grades !== 'All') {
          const db_response = await postgres.query(queries.getSchoolSeats(), [school.id])
          const seats = db_response.rows
          if(seats.length > 0) {
            const seats_info = seats.filter(v => v.grade === grades)[0]
            if(seats_info) {
              school.seatsInfo = seats_info
            }
          }
        }
      }
    }
    if(!params.ignore) {
      const search_history = await saveSearchHistory(params, user_id || params.userId, ip)
      await saveSearchHistorySchools(search_history.id, schools)
    } else {
      if(params.bestMatch && params.userId) {
        // Get best match
        const leads = await getLeadsByUserId(params.userId)
        const best_match = schools.filter( school => leads.findIndex(lead => lead.school_id === school.schoolId
          && `${lead.child_first_name} ${lead.child_last_name}` !== params.studentName) === -1).slice(0, BEST_MATCH_SIZE)
        return {
          pagination: search.getPaginationData({
            page: params.page,
            pageSize: params.pageSize
          }, search_result),
          results: result_schools,
          bestMatch: best_match,
        }
      }
    }
    return {
      pagination: search.getPaginationData({
        page: params.page,
        pageSize: params.pageSize
      }, search_result),
      results: result_schools
    }
  } catch (error) {
    logger.error('searchSchoolsV1Error:', error)
    throw serverError
  }
}

export async function updateSchoolTourTimes(school_id: number, params: AddSchoolTourTimes) {
  await validate(params, schemas.add_tour_times)
  const school = await getSchoolById(school_id)
  if(!school) throw notFoundError
  try {
    const update_params = getTourTimesParams(params)
    const condition = {school_id}
    const built_update = buildUpdate('school_tour_times', condition, update_params)
    const db_response = await postgres.query(built_update.text, built_update.values)
    await updateAboutYourSchoolTourTimes(school_id, params)
    return db_response.rows[0]
  } catch (error) {
    logger.error('updateSchoolTourTimesError:', error)
    throw serverError
  }
}

export async function getSchoolTourTimes(school_id: number) {
  try {
    const db_response = await postgres.query(queries.getSchoolTourTimes(), [school_id])
    return db_response.rows
  } catch (error) {
    logger.error('getSchoolTourTimesError:', error)
    if(error.statusCode === notFoundError.statusCode) throw error
    throw serverError
  }
}

export async function getSchoolSitesReviews(school_id: number) {
  const external_reviews = await getSchoolExternalReview(school_id)
  if(external_reviews.length > 0) {
    await procesingExternalSites(external_reviews)
  }
  const schola_reviews = await getScholaReviews(school_id)
  return [...external_reviews, schola_reviews]
}

export async function addUpdateSchoolExternalReviews(school_id: number, params: SchoolExternalReviews) {
  await validate(params, schemas.school_external_reviews)
  const school = await getSchoolById(school_id)
  if(!school) throw notFoundError
  const external_reviews = await getSchoolExternalReview(school_id)
  const add_payload: ExternalReviewPayload[] = []
  const update_payload: ExternalReviewPayload[] = []
  params.externalReviews.forEach( review => {
    const current_review = external_reviews.find( external_review => external_review.site === review.site)
    if(current_review) {
      update_payload.push({
        id: current_review.id,
        school_id,
        payload: review
      })
    } else {
      add_payload.push({
        school_id,
        payload: review
      })
    }
  })

  await Promise.all(update_payload.map( elem => {
    updateSchoolExternalReviews(elem.school_id, elem.id, elem.payload)
  }))
  const inserts = await addSchoolExternalReviews(add_payload)
  return inserts
}

export async function getSchoolViews(school_id: number) {
  try {
    const db_response = await postgres.query(queries.getSchoolViews(), [school_id])
    if(db_response.rowCount === 0) throw notFoundError
    const school_views = db_response.rows
    return school_views
  } catch (error) {
    logger.error('getSchoolViewsError:', error)
    if(error.statusCode === notFoundError.statusCode) throw error
    throw serverError
  }
}

export async function updateSchoolMarketing(school_id: number, params: UpdateSchoolMarketing) {
  await validate(params, schemas.update_school_marketing)
  const school = await getSchoolById(school_id)
  if(!school) throw notFoundError
  try {
    const featured_subscription_end = new Date()
    featured_subscription_end.setDate(featured_subscription_end.getDate() + params.addFeatureDays)
    let additional_connects = school.additional_connects
    if(school.additional_connects) {
      additional_connects += params.addConnects
      additional_connects = additional_connects < 0 ? 0 : additional_connects
    }

    const update_params = {
      featured_subscription_end,
      additional_connects
    }
    const condition = {id: school_id}
    const built_update = buildUpdate('schools', condition, update_params)
    const db_response = await postgres.query(built_update.text, built_update.values)
    return db_response.rows[0]
  } catch (error) {
    logger.error('updateSchoolMarketingError:', error)
    throw serverError
  }
}

export async function updateSchoolEmailMarketing(school_id: number, status: boolean) {
  const school = await getSchoolById(school_id)
  if(!school) throw notFoundError
  try {
    const update_params = {
      email_marketing: status? 1 : 0
    }
    const condition = {id: school_id}
    const built_update = buildUpdate('schools', condition, update_params)
    const db_response = await postgres.query(built_update.text, built_update.values)

    const {users} = await getSchoolUsers(school_id)
    const user_ids = users.map( user => user.id)

    const user_details = await usersController.getUsersDetails(user_ids)
    const email_promises: Promise<unknown>[] = []
    user_details.forEach(user => {
      const html = schoolMarketingNotification(school.name, school.phone, user.email)
      email_promises.push(sendEmail({
        html,
        subject: `Email Marketing for School ${school.name}`,
        to: INFO_RECIPIENT,
      }))
    })

    await Promise.all(email_promises)

    return db_response.rows[0]
  } catch (error) {
    logger.error('updateSchoolEmailMarketingError:', error)
    throw serverError
  }
}

export async function newFeatureRequest(school_id: number, params: SchoolFeatureRequest) {
  await validate(params, schemas.feature_request)
  const school = await getSchoolById(school_id)
  if(!school) throw notFoundError
  const html = newFeatureRequestTemplate(school.name, params.featureName, params.suggestion)
  sendEmail({
    html,
    subject: `You have a new Feature Request by ${school.name} (${school.id})`,
    to: FEATURE_REQUEST_EMAIL,
    bcc: INFO_RECIPIENT,
    tags: ['featureRequest']
  }).catch( () => {
    logger.error('newFeatureRequest - sendEmailError')
  })
}

export async function updateEnrollementGoal(school_id: number, enrollment_goal: number) {
  const school = await getSchoolById(school_id)
  if(!school) throw notFoundError
  try {
    const update_params = {
      enrollment_goal
    }
    const condition = {id: school_id}
    const built_update = buildUpdate('schools', condition, update_params)
    const db_response = await postgres.query(built_update.text, built_update.values)
    return db_response.rows[0]
  } catch (error) {
    logger.error('updateEnrollementGoalError:', error)
    throw serverError
  }
}

async function addSchoolExternalReviews(params: Array<ExternalReviewPayload>) {
  try {
    if(params.length === 0) return []
    const now = moment().toDate()
    const insert_array = params.map( (review) => ({
      school_id: review.school_id,
      ranking: -1,
      total: -1,
      created_at: now,
      updated_at: now,
      ...review.payload,
    }))
    const batch_text = batchParametrizedInsertQuery('school_external_reviews', insert_array)
    const db_response = await postgres.query(batch_text, getObjectValuesFromArray(insert_array))
    await procesingExternalSites(db_response.rows)
    return db_response.rows
  } catch (error) {
    logger.error('addSchoolExternalReviewsError:', error)
    throw serverError
  }
}

async function updateSchoolExternalReviews(school_id: number, id: number, params: ExternalReview) {
  try {
    const update_params = {...params, updated_at: moment.utc()}
    const condition = {id, school_id}
    const built_update = buildUpdate('school_external_reviews', condition, update_params)
    const db_response = await postgres.query(built_update.text, built_update.values)
    return db_response.rows[0]
  } catch (error) {
    logger.error('updateSchoolExternalReviewsError:', error)
    throw serverError
  }
}

function procesingExternalSites(reviews: Array<any>, force_update = false) {
  const requests = []
  const now = moment()
  for (const review_data of reviews) {
    if(review_data.site !== 'Facebook' && review_data.site !== 'Niche' && review_data.site !== 'Google') {
      const last_update = moment(review_data?.updated_at || review_data.created_at)
      const difference = Math.round(now.diff(last_update, 'hours'))
      if(difference > EXTERNAL_SITE_HOUR_RANGE || force_update || review_data.total === -1) {
        if(review_data.url && validateUrl(review_data.url)) {
          requests.push(getSiteReviewRequest(review_data.url,review_data.site,review_data.id))
        }
      }
    }
  }
  return Promise.all(requests)
}

async function getSiteReviewRequest(url: string, site: string, id: number) {
  let data:SchoolExternalReview
  switch(site) {
  case 'Google':
    data = await getGoogleReviews(url)
    break
  case 'Niche':
    data = await getNicheReviews(url)
    break
  case 'Great Schools':
    data = await getGreatSchoolsReviews(url)
    break
  case 'Facebook':
    data = await getFacebookReviews(url)
    break
  }
  return await updateSchoolExternalReview(id, data.rate, data.reviews)
}

async function updateSchoolExternalReview(id: number, ranking: number, total: number) {
  try {
    const update_params = {ranking, total, updated_at: moment.utc()}
    const condition = {id}
    const built_update = buildUpdate('school_external_reviews', condition, update_params)
    const db_response = await postgres.query(built_update.text, built_update.values)
    return db_response.rows[0]
  } catch (error) {
    logger.error('updateSchoolExternalReviewsError:', error)
    throw serverError
  }
}

async function getGoogleReviews(url: string): Promise<SchoolExternalReview> {
  const result:SchoolExternalReview = {
    rate: 0,
    reviews: 0
  }
  const url_arr = url.split('lrd=')
  if(url_arr.length > 1) {
    const feature_id = url_arr[1].split(',')[0]
    try {
      const axios_response = await axios.get(`https://www.google.com/async/reviewDialog?async=feature_id:${feature_id},review_source:All%20reviews,sort_by:qualityScore,start_index:0,is_owner:false,filter_text:,next_page_token:,async_id_prefix:,_pms:s,_fmt:pc`)
      const { data } = axios_response
      let rate_index = 0
      let total_index = 0
      GOOGLE_EXTERNAL_MARKS.forEach(external_mark => {
        const found = (rate_index > 0 || total_index > 0)
        if(!found) {
          rate_index = data.search(external_mark.rate_class)
          total_index = data.search(external_mark.total_class)
          if(rate_index > 0) {
            let element = data.substring(rate_index, rate_index + external_mark.rate_class.length + 50)
            element = element.split('>')[1]
            element = element.split('<').find( (ex: string) => !Number.isNaN(ex))
            result.rate = parseFloat(element)
          }
          if(total_index > 0) {
            let element = data.substring(total_index, total_index + external_mark.total_class.length + 50)
            element = element.split('>')[1]
            element = element.split(' ').find( (ex: string) => !Number.isNaN(ex))
            result.reviews = parseFloat(element)
          }
        }
      })
    } catch (error) {
      logger.error('getGoogleReviewsError:',  error.statusCode, error.message, url)
      return result
    }
  }
  return result
}

async function getNicheReviews(url: string): Promise<SchoolExternalReview> {
  const result:SchoolExternalReview = {
    rate: 0,
    reviews: 0
  }
  try {
    const axios_response = await axios.get(url, {
      headers: {
        'User-Agent': 'Request-Promise',
      }
    })
    const { data } = axios_response
    const star_pos = data.search(NICHE_STAR_STRING)
    const review_pos = data.search(NICHE_REVIEW_STRING)

    if(star_pos > 0) {
      const element = data.substring(star_pos + NICHE_STAR_STRING.length, star_pos + NICHE_STAR_STRING.length + 10)
      const elements = element.split('-')
      const rate = elements[0]
      let final = ''

      if (elements.length == 1) {
        final = rate.split('"')[0]
        result.rate = NICHE_RATES.indexOf(final.toUpperCase())
      } else {
        if (elements[1].includes('plus')) {
          final = `${rate}+`
          result.rate = NICHE_RATES.indexOf(final.toUpperCase())
        }
        else if (elements[1].includes('minus')) {
          final = `${rate}-`
          result.rate = NICHE_RATES.indexOf(final.toUpperCase())
        }
      }
    }

    if (review_pos > 0) {
      result.reviews = data.substring(review_pos + NICHE_REVIEW_STRING.length, review_pos + NICHE_REVIEW_STRING.length + 10).split(' ')[0]
    }
    return result
  } catch (error) {
    logger.error('getNicheReviewsError:', error)
    return result
  }
}

async function getGreatSchoolsReviews(url: string): Promise<SchoolExternalReview> {
  const result:SchoolExternalReview = {
    rate: 0,
    reviews: 0
  }
  try {
    const axios_response = await axios.get(url)
    const { data } = axios_response
    const star_pos = data.search(GREAT_SCHOOLS_STAR_STRING)
    const review_pos = data.search(GREAT_SCHOOLS_REVIEW_STRING)

    if(star_pos > 0) {
      const element = data.substring(star_pos + GREAT_SCHOOLS_STAR_STRING.length, review_pos)
      const element_arr = (element.match(/filled/g)||[])
      result.rate = element_arr.length
    }

    if(review_pos > 0) {
      result.reviews = data.substring(review_pos + GREAT_SCHOOLS_REVIEW_STRING.length, review_pos + GREAT_SCHOOLS_REVIEW_STRING.length + 20).trim().split(' ')[0]
    }

    return result
  } catch (error) {
    logger.error('getGreatSchoolsReviewsError:',  error.statusCode, error.message, url)
    return result
  }
}

async function getFacebookReviews(url: string): Promise<SchoolExternalReview> {
  const result:SchoolExternalReview = {
    rate: 0,
    reviews: 0
  }
  try {
    let formated_url = url.endsWith('/') ? url : url + '/'
    if(url.indexOf('reviews') === -1) {
      formated_url = url + 'reviews/'
    }
    const axios_response = await axios.get(formated_url, {
      headers: {
        'User-Agent': 'PostmanRuntime/7.26.8',
      }
    })
    const data = axios_response.data
    const rateClass = FACEBOOK_RATE_CLASS
    const totalClass = FACEBOOK_TOTAL_CLASS
    const rate_index = data.indexOf(rateClass)
    const total_index = data.indexOf(totalClass)
    if (rate_index > 0) {
      const element = data.substring(rate_index + rateClass.length + 2, rate_index + rateClass.length + 5)
      result.rate = parseFloat(element)
    }
    if (total_index > 0) {
      let element = data.substring(total_index + rateClass.length + 2, total_index + totalClass.length + 50)
      element = element.split('<')[0]
      element = element.split(' ').find( (ex: string) => !Number.isNaN(ex))
      result.reviews = parseFloat(element)
    }
    return result
  } catch (err) {
    logger.error('getFacebookReviewsError:', err)
    return result
  }
}

function validateUrl(url: string) {
  try {
    new URL(url)
    return true
  } catch (error) {
    return false
  }
}

async function getSchoolExternalReview(school_id: number) {
  try {
    const db_response = await postgres.query(queries.getSchoolExternalReview(), [school_id])
    return db_response.rows
  } catch (error) {
    logger.error('getSchoolExternalReviewError:', error)
    throw serverError
  }
}

async function getScholaReviews(school_id: number) {
  try {
    const db_response = await postgres.query(queries.getScholaReviews(), [school_id])
    const ranking = db_response.rowCount > 0 ? db_response.rows.reduce( (t, n) => t + Math.floor((n.parent_satisfaction + n.student_happiness) / 2), 0) / db_response.rowCount : 0
    return {
      id: 0, // This value is hardcoded, needs further review
      school_id,
      site: 'Schola', // This value is hardcoded, needs further review
      url: `/schools/${school_id}`,
      ranking,
      total: db_response.rowCount,
    }
  } catch (error) {
    logger.error('getScholaReviewsError:', error)
    throw serverError
  }
}

async function updateAboutYourSchoolTourTimes(school_id: number, params: AddSchoolTourTimes) {
  try {
    const update_params = getTourTimesParams(params)
    const condition = {school_id}
    const built_update = buildUpdate('about_your_school_tour_times', condition, update_params)
    await postgres.query(built_update.text, built_update.values)
  } catch (error) {
    logger.error('updateAboutYourSchoolTourTimesError:', error)
    throw serverError
  }
}

function getTourTimesParams(params: AddSchoolTourTimes) {
  return {
    week_availability: params.weekAvailability,
    morning_start: params.morningStart,
    morning_end: params.morningEnd,
    afternoon_start: params.afternoonStart || null,
    afternoon_end: params.afternoonEnd || null,
  }
}

async function getLeadsByUserId(query_user_id: string) {
  try {
    const db_response = await postgres.query(leads_queries.getLeadsByUserId(), [query_user_id])
    return db_response.rows
  } catch (error) {
    logger.error('getBestMatchError:', error)
    throw serverError
  }
}

function buildFeatures(feature_array: Array<SearchSchoolFeature>) {
  const features = _.flatMap(feature_array, (item:SearchSchoolFeature, key: string) => {
    if (!item[key]) {
      return {
        id: item.i,
        weight: item.w,
      }
    }

    return _.map(item, (obj: SearchSchoolFeature) => ({
      id: obj.i,
      weight: obj.w,
    }))
  })
  return features
}

async function saveSearchHistorySchools(id: number, schools: SchoolIds[]) {
  try {
    const items = schools.map((school, index) => ({
      search_history_id: id,
      school_id: school.schoolId,
      position: index + 1,
      features_matched: school.features_matched ?? null,
      match_percent: school.match_percent ?? null,
    }))
    if (items.length) {
      const batch_text = batchParametrizedInsertQuery('search_history_schools', items)
      await postgres.query(batch_text, getObjectValuesFromArray(items))
    }
  } catch (error) {
    logger.error('saveSearchHistorySchoolsError:', error)
    throw serverError
  }
}

export async function saveSearchHistory(params: SearchSchoolV2, user_id?: string, ip?: string) {
  const ignore = (params.ignore === 'true')
  if(ignore) return {id: 0}
  try {
    const now = moment().toDate()
    const save_search_history_params: SearchSchoolHistory = {
      ip: params.ip || null,
      state: params.state || null,
      zip: params.zip || null,
      lat: params.lat,
      lon: params.lon,
      address: params.address,
      grade: params.grades ? params.grades.join(','): '',
      school_types: params.school_types ? params.school_types.join(','): '',
      created_at: now,
      updated_at: now,
    }
    if(user_id && user_id !== '') save_search_history_params.user_id = user_id
    if(params.enrolling) save_search_history_params.enrolling = params.enrolling
    if(ip) save_search_history_params.ip = ip
    const values = await getObjectValues(save_search_history_params)
    const db_response = await postgres.query(insertQuery('search_history', save_search_history_params), values)
    searchHistory.saveSearchFeaturesHistory(params.f, db_response.rows[0])
      .catch(error=>logger.warn('saveSearcFeatureshHistoryError:', error))
    return db_response.rows[0]
  } catch (error) {
    logger.error('saveSearchHistoryError:', error)
    throw serverError
  }
}

function checkEmailDomain(email: string) {
  if(email.includes('@')) {
    const email_domain = email.split('@')[1]
    return COMMON_DOMAIN_LIST.includes(email_domain)
  }
  return false
}

async function sendClaimSchoolCode(code: string, school_name: string, school_id: number, email: string, phone: string) {
  try {
    await sendEmail({
      subject: `Claim School - Code verification`,
      html: claim_validation(school_name, code),
      to: email,
      bcc: INFO_RECIPIENT,
      tags: ['school-claim', 'code-validation']
    })
    await sendSms({
      from: TWILIO_FROM,
      body: `${CLAIM_SCHOOL_MESSAGE} ${code}`,
      to: phone
    })
  } catch (error) {
    logger.error('sendClaimSchoolCodeError:', error)
    throw serverError
  }
}

export async function syncAboutYourSchoolEnrollment(school_id: number, params: Array<AddSchoolSeats>) {
  const {enrollment_goal, current_enrollment} = getEnrollmentGoalAndCurrentEnrollment(params)
  try {
    const db_response = await postgres.query(about_your_school_queries.getAboutYourSchoolBySchoolId(), [school_id])
    if(db_response.rowCount > 0) {
      const about_your_school_id = db_response.rows[0].id
      await postgres.query(about_your_school_queries.deleteAboutYourSchoolEnrollments(), [about_your_school_id])
      const enrollment_array = params.map( element => ({
        school_id,
        about_your_school_id,
        grade: element.grade,
        current_students: Number(element.total) - Number(element.availables),
        target_students: element.total,
        created_at: moment().toDate()
      }))
      const batch_text = batchParametrizedInsertQuery(SCHOLA_TABLES.ABOUT_YOUR_SCHOOL_ENROLLMENTS, enrollment_array)
      await postgres.query(batch_text, getObjectValuesFromArray(enrollment_array))
    }
    await updateEnrollmentGoal(school_id, enrollment_goal, current_enrollment)
  } catch (error) {
    logger.error('syncAboutYourSchoolEnrollmentError:', error)
    throw serverError
  }
}

export async function syncOnboardingEnrollments(school_id: number, params: Array<AddSchoolSeats>) {
  const {enrollment_goal, current_enrollment} = getEnrollmentGoalAndCurrentEnrollment(params)
  try {
    const db_response = await postgres.query(onboardings_queries.getOnboarding(), [school_id])
    if(db_response.rowCount > 0) {
      const onboarding_id = db_response.rows[0].id
      await postgres.query(onboardings_queries.deleteOnboardings(), [onboarding_id, school_id])
      const onboarding_enrollments = params.map( element => ({
        onboarding_id,
        school_id,
        grade: element.grade,
        current_students: Number(element.total) - Number(element.availables),
        goal_students: element.total
      }))
      const batch_text = batchParametrizedInsertQuery('onboarding_enrollments', onboarding_enrollments)
      await postgres.query(batch_text, getObjectValuesFromArray(onboarding_enrollments))
    }
    await updateEnrollmentGoal(school_id, enrollment_goal, current_enrollment)
  } catch (error) {
    logger.error('syncOnboardingEnrollmentsError:', error)
    throw serverError
  }
}

function getEnrollmentGoalAndCurrentEnrollment(params: Array<AddSchoolSeats>) {
  let enrollment_goal = 0
  let current_enrollment = 0
  params.forEach( element => {
    current_enrollment += Number(element.total) - Number(element.availables)
    enrollment_goal += Number(element.total)
  })
  return { enrollment_goal, current_enrollment }
}

async function updateEnrollmentGoal(school_id: number, enrollment_goal: number, current_enrollment: number) {
  try {
    const condition = {id: school_id}
    const built_update = buildUpdate('schools', condition, {
      enrollment_goal,
      current_enrollment
    })
    const db_response = await postgres.query(built_update.text, built_update.values)
    return db_response.rows[0]
  } catch (error) {
    logger.error('updateEnrollmentGoalError:', error)
    throw serverError
  }
}

async function cloneNotes(tour_id: number, lead_id: number) {
  try {
    let db_response = await postgres.query(notes_queries.getNotes(), ['tour', tour_id])
    if(db_response.rowCount === 0) return []
    const notes = db_response.rows
    const new_notes = notes.map( (obj: Note) => ({
      object_type : 'lead',
      object_id: lead_id,
      user_id: obj.user_id,
      note: obj.note,
      created_at:obj.created_at,
      updated_at: obj.updated_at
    }))
    const batch_text = batchParametrizedInsertQuery('notes', new_notes)
    db_response = await postgres.query(batch_text, getObjectValuesFromArray(new_notes))
    return db_response.rows
  } catch (error) {
    logger.error('cloneNotesError:', error)
    throw serverError
  }
}

async function createTourNote(user_id: string, tour_status: string, tour_id: number) {
  if(tour_status === 'completed') {
    const user_details = await usersController.getUserDetails(user_id)
    notesController.createNote({
      object_type: 'tour',
      object_id: tour_id,
      user_id: user_details.email,
      note: 'Completed tour'
    })
  }
}

async function updateProfilePicture(school_id: number, image_id: number) {
  try {
    // Set the new profile picture
    await postgres.query(queries.setProfilePicture(), [school_id, image_id])
    // Set the profile_image property for the rest of the records in school_images to FALSE
    await postgres.query(queries.setOtherImagesNonProfilePictures(), [school_id, image_id])
  } catch (error) {
    logger.error('updateProfilePictureError:', error)
    throw serverError
  }
}

async function getDataAndPagination(params: LocationAndPagination, count_query: string, data_query: string, options?: TopOptions) {
  const page_rows = Number(params.pageSize) || 10
  const page = Number(params.page) || 1
  const page_offset = (page - 1) * page_rows
  const values:Array<number | string> = [page_rows, page_offset]
  const result = {
    pagination: {
      page,
      pageSize: page_rows,
      rowCount: 0,
      pageCount: 0,
    },
    data: <unknown[]>[]
  }
  try {
    let db_response = await postgres.query(count_query)
    const count = Number(db_response.rows[0].count)
    result.pagination.rowCount = count
    result.pagination.pageCount = Math.ceil(count/page_rows)

    if(params.lat && params.lon) {
      values.unshift(params.lat, params.lon)
    } else if(options?.lat_lon_required) {
      return result
    }
    if(options?.address_required) {
      values.unshift(params.country)
      if(params.zipcode) values.push(params.zipcode)
      else if(params.state && params.city) values.push(params.city, params.state)
      else return result
    }
    db_response = await postgres.query(data_query, values)
    const data = db_response.rows
    if(options?.school_url) {
      data.forEach(school => {
        school.schoolUrl = getSchoolUrl(school)
      })
    }
    result.data = data
    return result
  } catch (error) {
    logger.error('getDataAndPaginationError:', error, data_query, values)
    throw serverError
  }
}

async function verifyClaimCode(school_id: number, code: string) {
  try {
    const db_response = await postgres.query(queries.getClaimCode(), [school_id, code])
    return db_response.rows[0]
  } catch (error) {
    logger.error('verifyClaimCodeError:', error)
    throw serverError
  }
}

async function sendSchoolClaimEmail(params: SchoolClaim, claimType='') {
  try {
    return await sendEmail({
      subject: `School claim from: ${params.firstName} ${params.lastName} ${claimType}`,
      html: school_claim(params),
      to: params.email,
      bcc: REROUTE_1,
      tags: ['school-claim', 'notification']
    })
  } catch (error) {
    logger.error('sendSchoolClaimEmailError:', error)
    throw serverError
  }
}

export async function removeSchoolImagesDB(key: string, school_id: number, subfolder: string, image_id: number) {
  try {
    const new_gallery_array = [
      key,
      `l-${key}`,
      `m-${key}`,
      `s-${key}`,
      `original-${key}`,
    ]
    const s3_delete_promises = new_gallery_array.map(async (key) => await deleteFile(`${subfolder}/${key}`))
    const db_response = await postgres.query(queries.deleteSchoolImageById(), [image_id, school_id])
    await Promise.all(s3_delete_promises)
    return db_response.rows
  } catch (error) {
    logger.error('removeSchoolImageError:', error)
    throw serverError
  }
}

export async function getSchoolImage(school_id: number, image_id: number) {
  try {
    const db_result = await postgres.query(queries.getSchoolImageById(), [image_id, school_id])
    return db_result.rows[0]
  } catch (error) {
    logger.error('getSchoolImageError:', error)
    throw serverError
  }
}

async function sendSchoolCustomApplicationEmail(school_name: string, school_id: number, application_key: string) {
  try {
    return await sendEmail({
      subject: 'New Custom Application',
      html: school_custom_application(
        school_name,
        `https://${S3_BUCKET}.s3.amazonaws.com/${application_key}`,
        school_id
      ),
      to: INFO_RECIPIENT,
      bcc: REROUTE_3
    })
  } catch (error) {
    logger.error('sendUserCreatedEmailError:', error)
    throw serverError
  }
}

async function sendUserCreatedEmail(email: string, school_name: string, school_id: number) {
  try {
    return await sendEmail({
      subject: '**Welcome to Schola**',
      html: school_new_user(school_name, `admin/v2/schools/${school_id}/dashboard`),
      to: email,
      bcc: REROUTE_3,
      tags: ['new-user-attached']
    })
  } catch (error) {
    logger.error('sendUserCreatedEmailError:', error)
    throw serverError
  }
}


async function sendUserCreatedEmailMerged(email: string, school_name: string, school_id: number, link: string) {
  try {
    return await sendEmail({
      subject: '**Welcome to Schola**',
      html: school_new_user_added(school_name, `admin/v2/schools/${school_id}/dashboard`, link),
      to: email,
      bcc: REROUTE_3,
      tags: ['new-user-created']
    })
  } catch (error) {
    logger.error('sendUserCreatedEmailError:', error)
    throw serverError
  }
}

async function createPasswordChangeTicket(user_id: string) {
  try {
    const ticket = await auth0.createPasswordChangeTicket(user_id)
    return ticket
  } catch (error) {
    logger.error('createPasswordChangeTicketError:', error)
    throw serverError
  }
}

/**
 * @param  {string} name
 * @returns {Promise} returns an object with the response from the database
 */
async function createActivatedUser(email: string, school_name: string, sendmail=true, createUser=true, user_id?:string){
  await validate({email}, schemas.validate_email)
  try {

    let user
    if(createUser){
      const temporary_password = uuid.v4() + 'aA1'
      user = await auth0.createUser({
        email,
        password: temporary_password,
        is_activated: true
      })
    } else {
      user = {  user_id: user_id}
    }

    const ticket = await createPasswordChangeTicket(user.user_id)
    if(sendmail) {
      await sendEmail({
        subject: 'Welcome to Schola',
        html: school_user_creation(school_name, ticket.link),
        to: email
      })
    }

    return { user, ticket }
  } catch (error) {
    logger.error('createUserError:', error)
    if(error.statusCode === notFoundError.statusCode) throw notFoundError
    throw serverError
  }
}

// Validation pending, function is still in WIP
export async function updateSpedFeatures(school_id: number) {
  try {
    const db_response = await postgres.query(features_queries.getFeaturesSchoolsWithFeatureGroup(), [school_id])
    const features_schools = db_response.rows
    const features = _.filter(features_schools, (fs) => fs.feature_group_id !== 8) // 8 is special education
    return features
  } catch (error) {
    logger.error('updateSpedFeaturesError:', error)
    throw serverError
  }
}

export async function getSchoolById(school_id: number) {
  try {
    const db_response = await postgres.query(queries.getSchoolById(), [school_id])
    return db_response.rows[0]
  } catch (error) {
    logger.error('getSchoolByIdError:', error)
    throw serverError
  }
}

export async function updateSchoolVideos(school_id: number, videos: Array<string>, client: PoolClient) {
  await deleteSchoolVideos(school_id, client)
  try {
    // Insert the school videos array into the database
    if(videos.length < 1) return {}
    const now = moment.utc()
    const school_videos = videos.map((video) => {
      let match = video.match(YOUTUBE_REGEXP)
      const result = {school_id, embed_code: video, created_at: now}
      if(match && match[1].length) {
        result.embed_code = `https://www.youtube.com/embed/${match[1]}`
      } else {
        match = video.match(VIMEO_REGEXP)
        if(match && match[1].length) {
          result.embed_code = `https://player.vimeo.com/video/${match[1]}`
        }
      }
      return result
    })
    const insert_batch_text = batchParametrizedInsertQuery('school_videos', school_videos)
    const db_result = await client.query(insert_batch_text, getObjectValuesFromArray(school_videos))
    return db_result.rows[0]
  } catch (error) {
    logger.error('updateSchoolVideosError:', error)
    throw serverError
  }
}

async function deleteSchoolVideos(school_id: number, client: PoolClient) {
  try {
    await client.query(queries.deleteSchoolVideos(), [school_id])
    return true
  } catch (error) {
    logger.error('deleteSchoolVideosError:', error)
    throw serverError
  }
}

async function schoolIsDuplicated(params: PostSchool) {
  try {
    const db_result = await postgres.query(queries.getSchoolForCreate(), [
      params.name,
      params.address,
      params.city,
      params.state,
      params.zip
    ])
    return db_result.rowCount > 0
  } catch (error) {
    logger.error('schoolGradesError:', error)
    throw serverError
  }
}

async function schoolGrades( school_id: number ) {
  try {
    const db_result = await postgres.query(queries.getSchoolGrades(), [school_id])
    return db_result.rows[0]
  } catch (error) {
    logger.error('schoolGradesError:', error)
    throw serverError
  }
}

async function getAllMatchingName(pagination: Pagination, options: QueryOptions, search_field?: string) {
  const page_rows = Number(pagination.pageSize) || 10
  const page = Number(pagination.page) || 1
  const page_offset = (page - 1) * page_rows
  const values: Array<unknown> = [page_rows, page_offset]
  if(options.user_id && options.user_id !== 'only') values.unshift(options.user_id)
  if(options.state) values.unshift(options.state)
  if(options.whitelist) values.unshift(options.whitelist)
  if(search_field) {
    const number_search_field = Number(search_field)
    if(number_search_field >= MIN_INTEGER && number_search_field <= MAX_INTEGER) {
      values.unshift(number_search_field)
    } else {
      values.unshift(-1)
    }
    values.unshift(`%${search_field}%`)
  }
  try {
    let db_result = await postgres.query(queries.getAllMatchingName(search_field, options), values)
    const schools = db_result.rows
    db_result = await postgres.query(queries.getAllMatchingNameCount(search_field, options), values.slice(0, -2))
    const row_count = Number(db_result.rows[0].count)
    return {schools, row_count}
  } catch (error) {
    logger.error('getAllMatchingNameError:', error)
    throw serverError
  }
}

async function searchSchoolsSimple(name: string, pagination: Pagination) {
  try {
    let es_results = await search.searchSchoolsSimple(name, pagination) as Record<string, any>
    if(es_results.body.hits.hits.length === 0 && es_results.body.suggest['sug-name']) {
      const words: Array<string> = []
      es_results.body.suggest['sug-name'].map( (suggestion:any) => {
        let word = suggestion.text
        if(suggestion.options.length > 0) {
          word = suggestion.options[0].text
        }
        words.push(word)
      })
      es_results = await search.searchSchoolsSimple(words.join(' '), pagination) as Record<string, any>
    }
    let schools = es_results.body.hits.hits.map( (hit: any) => _.pick(hit._source, [
      'id',
      'name',
      'latitude',
      'longitude',
      'city',
      'state',
      'type',
      'permanently_closed',
      'address',
    ]))
    schools = schools.filter( (x:School) => (x.permanently_closed || false) === false )
    return schools
  } catch (error) {
    logger.error('schools-searchSchoolsSimpleError:', error)
    throw serverError
  }
}

async function getGroupsByGrades(grades: Array<string>) {
  try {
    const db_result = await postgres.query(queries.getGroupsByGrades(grades), [grades])
    return db_result.rows
  } catch (error) {
    logger.error('getGroupsByGradesError:', error)
    throw serverError
  }
}

export async function needFillRecruiterProForm(school: School) {
  const has_recruiterpro_access = (school.plan_id && moment(school.subscription_end).isAfter() && RECRUITER_PRO_ACCESS.includes(school.plan_id))
  try {
    let db_result = await postgres.query(about_your_school_queries.getLastAboutYourSchoolFinalizedAt(), [school.id])
    const is_recruiter_finalized = !!db_result.rows[0]?.finalized_at
    db_result = await postgres.query(recruiter_questions_queries.getLastRecruiterQuestionForSchool(), [school.id])
    const recruiter_questions_form = db_result.rows[0]
    const is_recruiter_form_completed = isRecruiterFormCompleted(recruiter_questions_form)
    const result = {
      need_complete_pro_form: false,
      has_recruiterpro_access: false
    }
    if(has_recruiterpro_access) {
      result.has_recruiterpro_access = true
      if(is_recruiter_finalized && !is_recruiter_form_completed) {
        result.need_complete_pro_form = true
      }
    }
    return result
  } catch (error) {
    logger.error('needFillRecruiterProFormError:', error)
    throw serverError
  }
}

function isRecruiterFormCompleted(recruiter_form: any) {
  if(!recruiter_form) return false

  if(!REQUIRED_FIELDS_RECRUITER_FORM.every(
    field_name => Object.hasOwn(recruiter_form, field_name)
  )) return false

  if(!NOT_NULL_FIELDS_RECRUITER_FORM.every(
    field_name => Object.hasOwn(recruiter_form, field_name)
  )) return false

  // Validate special cases for optional recruiter form fields
  if(recruiter_form.transportation_offered === true) {
    if(!recruiter_form.transportation_areas) return false
    if(!recruiter_form.transportation_bus_stops_times) return false
    if(recruiter_form.transportation_metro_bus_pass == null) return false
    if(!recruiter_form.about_transportation_iep) return false
  }
  if(recruiter_form.uniforms == true) {
    if(!recruiter_form.uniforms_colors) return false
    if(recruiter_form.uniforms_are_provided == null) return false
  }
  if(recruiter_form.offer_foreign_languages == true) {
    if(!recruiter_form.foreign_languages_offered) return false
  }
  if(recruiter_form.is_ieps_sped_equipped == true){
    if(recruiter_form.have_specific_coordinators == null) return false
    if(recruiter_form.type_attention == null) return false
  }
  return true
}

/**
 * @param {string} school_id
 * @returns
 */
export async function getSchoolLeaders(school_id: number) {
  const school = await getSchoolById(school_id)
  if (!school) throw notFoundError
  try {
    const db_response = await postgres.query(queries.getSchoolLeaders(school.city.replace(/'/g,"''"), school.state), [])
    return db_response.rows
  } catch (error) {
    logger.error('getSchoolLeaders:', error)
    throw serverError
  }
}

async function getStateContribution(school_id:number) {
  const decimal_regex = /^\d+|(\.\d{1,2})$/
  const school = await getSchoolById(school_id)
  const amount = decimal_regex.test(school.avg_per_pupil) ? parseFloat(school.avg_per_pupil) : 0
  if (amount > 0) return amount
  const data = await postgres.query(queries.getStateContribution(), [school_id])
  return (data && data.rows.length === 1 ? parseFloat(data.rows[0].amount.replace('$', '').replace(',', '')) : 0)
}
async function getStudentsAmount(school_id:number): Promise<Array<{label: string, value: number}>>  {
  //console.log('getStudentsAmount', school_id)
  // const data = await postgres.query(queries.getStudentsAmount(), [school_id])
  // const students_amount = []
  // if (data.rows.length === 1) {
  //   for (let index = 0; index <= 7; index++) {
  //     if (!isNaN(data.rows[0]['year201' + index])) {
  //       students_amount.push({
  //         label: '201' + index, value: (data.rows[0]['year201' + index] ? parseInt(data.rows[0]['year201' + index]) : 0),
  //       })
  //     }
  //   }
  // }
  // return students_amount
  return []
}
async function getSchoolAwareness(school_id:number) {
  // const school_viewshistory_count = await postgres.query(queries.countViewsHistory(), [school_id])
  // const search_history_schools_count = await postgres.query(queries.countSearchHistory(), [school_id])
  const data = await postgres.query(queries.countAwareness(), [school_id])
  return {
    profileClicks: {
      value: data.rows[0] ? data.rows[0].countclickhistory : 0,
      orientation: 'up'
    },
    profileView: {
      value: data.rows[0] ? data.rows[0].countviewshistory : 0,
      orientation: 'up'
    },
    searchAppearances: {
      value: data.rows[0] ? data.rows[0].countsearchhistory : 0 ,
      orientation: 'up'
    }
  }
}

async function getSchoolAppearances(school_id: number, from?: string) {
  let query = queries.searchAppearancesBySchool(school_id)
  if(from) {
    const formattedDate = `'${new Date(from).toISOString()}'`
    query += queries.appearanceFrom(formattedDate)
  }

  const appareances = await postgres.query(query, [])

  return appareances.rows[0].appearances
}

async function getMarketingEffort(school_id:number) {
  const response = await postgres.query(queries.getMarketingEffort(), [school_id])
  return response
}
async function getMarketingChannel(school_id:number) {
  const response = await postgres.query(queries.getMarketingChannel(), [school_id])
  return response
}
async function getCampaignStatus(school_id:number) {
  return {fb:<unknown>[]} // Not implemented
}
async function getSchoolMaxRevenue(school_id:number) {
  return 0 // Not implemented
}
async function getSchoolLevel(school_id:number) {
  return 1 // Not implemented
}
async function getSchoolEnrollement(school_id:number) {
  return 0 // Not implemented
}
async function getSchoolEngagement(school_id:number) {
  // Not implemented
  const base_value = {
    value: 0,
    orientation: 0
  }
  return {
    studentsInterested: base_value,
    schoolFavCount: base_value,
    requestedInfo: base_value,
    requestedTour: base_value,
    applicationsReview: base_value
  }
}

/**
 * @param {number} school_id
 */
export async function getSchoolDashboard(school_id: number, from?: string) {
  try {
    const [
      schoolEnrollement,
      schoolAwareness,
      schoolEngagement,
      schoolLevel,
      maxRevenue,
      stateContribution,
      studentsAmount,
      onboardingData,
      campaigStatus,
      marketingChannels,
      scholaMatchAppearances
    ] = await Promise.all([
      getSchoolEnrollement(school_id),
      getSchoolAwareness(school_id),
      getSchoolEngagement(school_id),
      getSchoolLevel(school_id),
      getSchoolMaxRevenue(school_id),
      getStateContribution(school_id),
      getStudentsAmount(school_id),
      getMarketingEffort(school_id),
      getCampaignStatus(school_id),
      getMarketingChannel(school_id),
      getSchoolAppearances(school_id, from)
    ])

    return {
      schoolEnrollement,
      schoolAwareness,
      schoolEngagement,
      schoolLevel,
      maxRevenue,
      stateContribution,
      studentsAmount,
      onboardingData: onboardingData.rows,
      campaigStatus: campaigStatus || {},
      marketingChannels: marketingChannels.rows,
      scholaMatchAppearances
    }
  } catch (error) {
    logger.error('getSchoolDashboard:', error)
    throw serverError
  }
}

/**
 * @param {number} school_id school_id to update
 * @param {string} user_id user to notify
 */
export async function sendScholaBoostsNotification(school_id:number, user_id:string) {
  const school = await getSchoolById(school_id)
  if (!school) throw notFoundError
  try {
    const user = await usersController.getUserDetails(user_id)
    return await featuresController.sendScholaBoostsNotification({
      name: user.name, email: user.email, school: school.name
    })
  } catch (error) {
    logger.error('sendScholaBoostsNotification:', error)
    throw serverError
  }
}

/**
 * @param {number} school_id
 * @param {BuyScholaBoosts} params
 */
export async function buyScholaBoosts(school_id:number, params: BuyScholaBoosts) {
  const school = await getSchoolById(school_id)
  if (!school) throw notFoundError
  try {
    let source = params.token
    let stripe_customer_id = school.stripe_customer_id
    params.description += '-' + school.name
    if (!stripe_customer_id) {
      stripe_customer_id = await createCustomerFromSchool(
        school_id,
        `${school.name} - ${school.id}`,
        source,
      )
      source = ''
    }
    const data = {
      amount: params.total * 100,
      currency: ONE_TIME_PAYMENT.beFeaturedSchool.currency,
      description: params.description,
      customer: stripe_customer_id
    }
    if (source !== ''){
      source = (await stripe.customers.createSource(stripe_customer_id, {source})).id
      Object.assign(data, {source})
    }
    await stripe.charges.create(data)

    const schola_boosts = {
      quantity: params.quantity,
      amount: params.amount,
      total: params.total,
      description: params.description,
      type: params.type,
      user_id: params.user_id,
    }
    await addSchoolScholaBoosts(school_id, schola_boosts)
    const html = schoolPurchaseScholaBoosts(
      school.name,
      params.quantity,
      params.amount,
      params.total,
      FRONTEND_HOST_ADMIN,
      school_id,
    )
    const email_options = {
      html,
      to: INFO_RECIPIENT,
      from: FROM_EMAIL,
      subject: 'Purchase ScholaBoosts',
    }
    await sendEmail(email_options)
    return true
  } catch (error) {
    logger.error('buyScholaBoosts:', error)
    throw serverError
  }
}

async function addSchoolScholaBoosts(school_id:number, school_data:BuyScholaBoosts) {
  try {
    const insert_params = {
      school_id,
      created_at: moment().toDate(),
      ...school_data
    }
    const values = await getObjectValues(insert_params)
    const db_response = await postgres.query(insertQuery('school_scholaboosts', insert_params), values)
    return db_response.rows[0]
  } catch (error) {
    logger.error('addSchoolScholaBoosts:', error)
    throw serverError
  }
}

async function createCustomerFromSchool(school_id:number, description: string, token:string, email?: string) {
  try {
    let customer = await stripe.customers.create({ name: description, description, email })
    const payment_method = await stripe.paymentMethods.attach(token, { customer: customer.id })
    customer = await stripe.customers.update(
      customer.id,
      {
        invoice_settings: { default_payment_method: payment_method.id}
      }
    )
    const school_update = buildUpdate('schools', {id: school_id}, { stripe_customer_id: customer.id })
    await postgres.query(school_update.text, school_update.values)
    return customer.id
  } catch (error) {
    logger.error('createCustomerFromSchool:', error)
    throw serverError
  }
}

/**
 * @param {BuyScholaBoostsACH} params buy schola boosts
 * @returns
 */
export async function buyScholaBoostsACH(school_id:number, params: BuyScholaBoostsACH) {
  const school = await getSchoolById(school_id)
  if (!school) throw notFoundError
  try {
    const { public_token, account_id, total, description, email } = params
    let stripe_customer_id = school.stripe_customer_id
    const settings = {
      clientID: PLAID_CONFIG.client_id,
      secret: PLAID_CONFIG.secret,
      env: PLAID_CONFIG.plaid_env,
      options:{},
    }
    const client = new plaid.Client(settings)

    const plaid_response = await client.exchangePublicToken(public_token)
    const stripe_token = await client.createStripeToken(plaid_response.access_token, account_id)
    if (stripe_customer_id) {
      await stripe.customers.update(stripe_customer_id, { source: stripe_token.stripe_bank_account_token })
    } else {
      stripe_customer_id = await createCustomerFromSchool(
        school_id,
        `${school.name} - ${school.id}`,
        stripe_token.stripe_bank_account_token,
        email
      )
    }

    const data = {
      amount: total * 100,
      currency: ONE_TIME_PAYMENT.beFeaturedSchool.currency,
      description: description,
      customer: stripe_customer_id
    }
    await stripe.charges.create(data)

    const schola_boosts = {
      quantity: params.quantity,
      amount: params.amount,
      total: params.total,
      description: params.description,
      type: params.type,
      user_id: params.user_id,
    }
    await addSchoolScholaBoosts(school_id, schola_boosts)
    const html = schoolPurchaseScholaBoosts(
      school.name,
      params.quantity,
      params.amount,
      params.total,
      FRONTEND_HOST_ADMIN,
      school_id,
    )
    const email_options = {
      html,
      to: INFO_RECIPIENT,
      from: FROM_EMAIL,
      subject: 'Purchase ScholaBoosts',
    }
    sendEmail(email_options).catch( () => {
      logger.error('buyScholaBoostsACH - sendEmailError')
    })
    return true
  } catch (error) {
    logger.error('buyScholaBoostsExist:', error)
    throw serverError
  }
}

/**
 * @param {BuyScholaBoostsExistMethod} params buy schola boost for existing customer
 * @returns
 */
export async function buyScholaBoostsExist(school_id:number, params: BuyScholaBoostsExistMethod) {
  const school = await getSchoolById(school_id)
  if (!school) throw notFoundError
  try {
    const data = {
      amount: params.amount * 100,
      currency: ONE_TIME_PAYMENT.beFeaturedSchool.currency,
      description: params.description,
      source: params.source_id,
      customer: params.customer_id
    }

    await stripe.charges.create(data)

    const schola_boosts = {
      quantity: params.quantity,
      amount: params.amount,
      total: params.total,
      description: params.description,
      type: params.type,
      user_id: params.user_id,
    }
    await addSchoolScholaBoosts(school_id, schola_boosts)
    const html = schoolPurchaseScholaBoosts(
      school.name,
      params.quantity,
      params.amount,
      params.total,
      FRONTEND_HOST_ADMIN,
      school_id,
    )
    const email_options = {
      html,
      to: INFO_RECIPIENT,
      from: FROM_EMAIL,
      subject: 'Purchase ScholaBoosts',
    }
    await sendEmail(email_options)
    return true
  } catch (error) {
    logger.error('buyScholaBoostsExist:', error)
    throw serverError
  }
}

/**
 * @param {EventInterested} params details of person interested in the event
 * @returns
 */
export async function handleEventInterestedContact(school_id:number, event_id:number, params:EventInterested) {
  try {
    const [school, users, event] = await Promise.all([
      getSchoolById(school_id),
      getSchoolUsers(school_id),
      eventsController.getEvent({school_id, id:event_id}),
    ])
    if (!school) throw notFoundError
    if (!event) throw notFoundError
    const user_details = await Promise.all(users.users.map((user) => usersController.getUserDetails(user.user_id)))
    user_details.forEach((user)=>{
      const { name, email } = user
      const interested_name = params.name
      const interested_email = params.email
      const html = eventInterestedSchoolNotification(name, event.description, interested_email, interested_name)
      const tag = 'events-interested'
      const email_options = {
        html,
        to: email,
        from: FROM_EMAIL,
        bcc: REROUTE_5,
        subject: 'There is someone interested in one of your events!',
        tags: ['notification', tag]
      }
      sendEmail(email_options)
    })
  } catch (error) {
    logger.error('handleEventInterestedContact:', error)
    if(error.statusCode === notFoundError.statusCode) throw error
    throw serverError
  }
}

/**
 * @param {number} school_id school_id to update
 * @param {string} avg_per_pupil new value
 */
export async function updateAvgPerPupil(school_id:number, avg_per_pupil: string) {
  try {
    const school_update = buildUpdate('schools', {id: school_id}, {avg_per_pupil})
    return await postgres.query(school_update.text, school_update.values)
  } catch (error) {
    logger.error('updateAvgPerPupil:', error)
    throw serverError
  }
}

/**
 * Sends SMS alert if query fails
 */
export async function pulse() {
  try {
    const db_response = await postgres.query(queries.getPulseSchool(), [])
    return {results: db_response.rows}
  } catch (error) {
    logger.error('pulse:', error)
    // send sms to alert failure
    try {
      await sendSms({
        from: TWILIO_FROM,
        body: `${PULSE_ERROR_SMS} - ${NODE_ENV || 'development'}`,
        to: PULSE_ERROR_PHONE
      })
    } catch (error) {
      logger.error('================= SMS PULSE ERROR =================',error)
    }
    logger.info('================= SMS PULSE OK =================')
    throw serverError
  }
}

/**
 * @param {string} school_id school id for new department
 * @param {string} name new department name
 */
export async function getSchoolUserDepartments(school_id: number) {
  try {
    const db_response = await postgres.query(queries.getSchoolDepartments(), [school_id])
    return db_response.rows
  } catch (error) {
    logger.error('getSchoolUserDepartments:', error)
    throw serverError
  }
}

/**
 * @param {string} school_id school id for new department
 * @param {string} name new department name
 */
export async function addSchoolUserDepartment(school_id: number, name:string) {
  try {
    const now = moment().toDate()
    const insert_params = {
      school_id,
      name,
      created_at: now,
      updated_at: now,
    }
    const values = await getObjectValues(insert_params)
    const db_response = await postgres.query(insertQuery('user_school_deparments', insert_params), values)
    return db_response.rows[0]
  } catch (error) {
    logger.error('addSchoolUserDepartment:', error)
    throw serverError
  }
}

/**
 * @param {number} school_id school id of user
 * @param {string} user_id id of user
 * @param {string} status new status
 * @param {string} updated_by user_id from auth token
 */
export async function updateUserSchool(school_id: number, user_id:string, status: string, updated_by: string) {
  try {
    const now = moment().toDate()

    const user_update = buildUpdate('users_schools', {school_id, user_id}, {status, updated_at: now})
    const [update_response, old_values] = [
      postgres.query(user_update.text, user_update.values),
      postgres.query(user_schools_queries.getUserSchoolBySchoolIdAndUserId(), [school_id, user_id])
    ]

    return await insertLogChange({
      table_name: 'user-school',
      user_id: updated_by,
      school_id: school_id,
      operation_type: 'edit',
      new_values: (await update_response).rows[0],
      old_values: (await old_values).rows[0],
      created_at: now,
      updated_at: now
    })
  } catch (error) {
    logger.error('updateUserSchool:', error)
    throw serverError
  }
}

/**
 * @param {number} school_id school id to update
 * @param {string} status new status
 * @param {string} source google by default
 */
export async function updateSchoolStatusDate(school_id: number, status:string, source = 'google') {
  try {
    const school = await getSchoolById(school_id)
    const now = moment()
    const payload = {
      review_business_status: status,
      review_business_status_date: now,
      review_business_status_source: source
    }
    if (!school) throw notFoundError
    if(status !== 'OK'){
      Object.assign(payload, { permanently_closed: true })
      if (status!==school.review_business_status) {
        //possibly closed
        sendReviewBusinessStatus(school.name)
      }
    }

    const school_update = buildUpdate('schools', {id: school_id}, payload)
    await postgres.query(school_update.text, school_update.values)
    school.review_business_status = payload.review_business_status
    school.review_business_status_date = payload.review_business_status_date
    school.review_business_status_source = payload.review_business_status_source
    return school
  } catch (error) {
    logger.error('updateSchoolStatusDate:', error)
    throw serverError
  }
}

/**
 * @param {SaveSearch} params Save search
 */
export async function saveSearch(params: SaveSearch) {
  await validate(params, schemas.save_search)
  const {schools, ...search} = params
  try {
    const search_history = await saveSearchHistory(search, params.userId , params.ip )
    await saveSearchHistorySchools(search_history.id, schools)
    return search_history
  } catch (error) {
    logger.error('saveSearch:', error)
    throw serverError
  }
}

/**
 * @param  {string} name Name of business for the review
 */
export function sendReviewBusinessStatus(name: string) {
  const html = businessStatus()
  const tag = 'business-status'
  const email_options = {
    html,
    to: INFO_RECIPIENT,
    from: FROM_EMAIL,
    subject: `Reviewing the Business Status of : ${name}`,
    tags: ['notification', tag]
  }
  sendEmail(email_options)
  return {
    html,
    tag
  }
}

export async function checkPaymentsEvergreen() {
  //get overdue subscriptions with evergreen payment
  const now = moment()
  try {
    // verify evergreen
    console.log('check evergreen')
    const overdue_schools = await postgres.query(queries.getOverdueSubscriptionSchoolsWithPaymentEvergreen())
    overdue_schools.rows.map( async (school) => {
      // Push subscription_end by monthly increments
      const subscription_end = moment(school.subscription_end)
      while (subscription_end < now) {
        subscription_end.add(1, 'months')
      }

      //update suscription
      const {text, values} = subscriptions_queries.getActiveSubscriptions(Number(school.id))
      const subscriptions = await postgres.query(text,values)
      if(subscriptions.rowCount>0 && subscriptions.rows[0].code === 'scholarecruiterpro-evergreen'){
        const { text, values } = subscriptions_queries.updateSubscription({ id: subscriptions.rows[0].id, end_date: subscription_end.toDate()})
        const { rowCount: rowCount2} = await postgres.query(text, values)
        if(rowCount2==0) console.log('evergreen - subscription not updated', school.id)
      }
      await this.updateSchool({  plan_id: 'scholarecruiterpro-evergreen', subscription_end: subscription_end}, school.id, 'checkPaymentsEvergreen' , ['system'])
    })

    // verify upcoming
    console.log('check upcoming')
    const overdue_upcoming_schools = await postgres.query(queries.getSubscriptionSchoolsWithPaymentUpcoming())
    overdue_upcoming_schools.rows.map( async (subscription) => {
      //update suscription
      const {text, values} = subscriptions_queries.getActiveSubscriptions(subscription.school_id)
      const subscriptions = await postgres.query(text,values)
      if(subscriptions.rowCount>0){
        //change active to finished
        const params = subscriptions_queries.updateSubscription({ id: subscriptions.rows[0].id, status: 'finished'})
        const { rowCount: rowCount2} = await postgres.query(params.text, params.values)
        if(rowCount2==0) console.log('upcoming - previous active subscription not updated', subscription.school_id)
      }
      //change upcoming to active
      const params2 = subscriptions_queries.updateSubscription({ id: subscription.id, status: 'active'})
      const { rowCount: rowCount3} = await postgres.query(params2.text, params2.values)
      if(rowCount3==0) console.log('upcoming - upcoming subscription not updated to active', subscription.school_id)

      const subscription_start = moment(subscription.start_date)
      const subscription_end = moment(subscription.end_date)
      await this.updateSchool({  plan_id: subscription.code, subscription_begin: subscription_start, subscription_end: subscription_end}, subscription.school_id, 'checkPaymentsUpcoming' , ['system'])
    })


  } catch (error) {
    logger.error('checkPaymentsEvergreen:', error)
    throw serverError
  }
}

export async function searchFeatures(params: SearchFeatures) {
  await validate(params, schemas.search_features)
  try {
    const features = buildFeatures(params.f)
    delete params.school_types
    const result = await searchSchools.features(params, features)
    return result
  } catch (error) {
    logger.error('searchFeaturesError:', error)
    throw serverError
  }
}

/**
 * Add or update school's events
 * @param {number} school_id
 * @param {AddOrUpdateEvent} events
 * @returns {Promise<void>}
 */
export async function addOrUpdateEvents(school_id: number, events: AddOrUpdateEvent[]): Promise<void> {
  await eventUtils.deleteBySchoolId(school_id)
  if(events && events.length > 0 ){
    const create_events = events.map((e: any) => ({
      event_at: e.event_at,
      description: e.description
    })) as CreateEvent[]
    await eventUtils.createSchoolEventsBatch(school_id, create_events)
  }
}

/**
 * Add or update school teacher stuff
 * @param {number} school_id
 * @param {AddUpdateSchoolTeacherStuffParams} params
 * @returns {Promise<void>}
 */
export async function addOrUpdateSchoolTeacherStuff(school_id: number, params: AddUpdateSchoolTeacherStuffParams): Promise<void> {
  if(!params){
    return
  }
  params = {
    school_id: school_id,
    perc_teachers_3ormoreyears: params.perc_teachers_3ormoreyears || '',
    perc_fulltime_teachers_certified: params.perc_fulltime_teachers_certified || '',
    average_teacher_salary: params.average_teacher_salary || '',
    nurse: params.nurse || '',
    psychologist: params.psychologist || '',
    social_worker: params.social_worker || '',
    law_enforcement_officer: params.law_enforcement_officer || '',
    security_guard: params.security_guard || '',
  }
  const db_response = await postgres.query(queries.getSchoolTeacherStaff(), [school_id])
  if(db_response.rowCount > 0){
    //update
    const id = db_response.rows[0].id as number
    const condition = { id }
    const built_update = buildUpdate(SCHOLA_TABLES.SCHOOL_TEACHERS_STAFF, condition, params)
    await postgres.query(built_update.text, built_update.values)
  }
  else{
    //insert
    const params_values = await getObjectValues(params)
    await postgres.query(insertQuery(SCHOLA_TABLES.SCHOOL_TEACHERS_STAFF, params), params_values)
  }
}

/**
 *
 * @param {number} school_id
 * @param {FastFact} params
 * @returns {Promise<object>}
 */
export async function createFastFacts(school_id: number, params: FastFact, imageUrl?:string): Promise<object> {
  await validate(params, schemas.fast_fact)
  const school = await getSchoolById(school_id)
  if (!school) throw notFoundError

  try {
    const subfolder = `school/${school_id}`
    const now = moment.utc()
    const insert_fast_fact: FastFactInsert = {
      school_id: school_id,
      title: params.title,
      description: params.description,
      image_url: imageUrl || '',
      created_at: now,
      updated_at: now
    }

    if (params.original && params.original.length) {
      const renamed_file = renameFile(params.original[0].originalname)
      const upload_result = await uploadLogo({
        original: params.original[0].buffer,
        subfolder: subfolder,
        filename: renamed_file,
        alt_img: params.file && params.file.length ? params.file[0].buffer : undefined
      }, {
        url: `https://${S3_BUCKET}.s3.amazonaws.com/school/${school_id}`,
        disable_random: true,
        is_school_image: true,
        is_profile_image: false
      })
      insert_fast_fact.image_url = `${subfolder}/${upload_result.key}`
    }

    const query = insertQuery('fast_facts', insert_fast_fact)
    const values = await getObjectValues(insert_fast_fact)
    const db_response = await postgres.query(query, values)

    const res = {
      school_id,
      id: db_response.rows[0].id,
      image_url: db_response.rows[0].image_url ? `//${S3_BUCKET}.s3.amazonaws.com/${db_response.rows[0].image_url}` : '',
      title: db_response.rows[0].title,
      description: db_response.rows[0].description,
    }

    return res
  } catch (error) {
    logger.error('createFastFacts:', error)
    throw serverError
  }
}


/**
 *
 * @param {number} school_id
 * @param {number} fast_fact_id
 * @returns {Promise<object>}
 */
export async function deleteFastFacts(school_id: number, fast_fact_id: number): Promise<object> {
  const school = await getSchoolById(school_id)
  if (!school) throw notFoundError
  try {
    const db_response = await postgres.query(queries.deleteFastFact(), [fast_fact_id,school_id])
    if (!db_response.rowCount) throw notFoundError
    const splited_name = db_response.rows[0].image_url.split('/')
    const file_name = splited_name[2]
    const subfolder = `${splited_name[0]}/${splited_name[1]}/`
    const delete_images = [
      `${subfolder}${file_name}`,
      `${subfolder}l-${file_name}`,
      `${subfolder}m-${file_name}`,
      `${subfolder}s-${file_name}`,
      `${subfolder}original-${file_name}`,
    ]
    await delete_images.map(async (image) => await deleteFile(image))
    return db_response.rows[0]
  } catch (error) {
    logger.error('createFastFacts:', error)
    throw serverError
  }
}

/**
 *
 * @param {number} school_id
 * @returns {Promise<object>}
 */
export async function getFastFacts(school_id: number): Promise<FastFacts> {
  const school = await getSchoolById(school_id)
  if (!school) throw notFoundError
  try {
    const db_response = await postgres.query(queries.getFastFacts(), [school_id])
    const fast_facts = db_response.rows.map((fastFact) => ({
      ...fastFact,
      full_url: `//${S3_BUCKET}.s3.amazonaws.com/${fastFact.image_url}`
    }))
    return { fast_facts }
  } catch (error) {
    logger.error('createFastFacts:', error)
    throw serverError
  }
}

/**
 *
 * @param {hubspotMatchSchool} params
 * @returns {Promise<object>}
 */
export async function hubspotMatchSchool(params: hubspotMatchSchool): Promise<object> {
  await validate(params, schemas.hubspot_match_school)
  try {
    if (params.state.length > 2) {
      const states_abbr_obj: any = fullname_states
      params.state = states_abbr_obj[params.state.toUpperCase()] || undefined
    }
    const values = [params.name, params.state, params.zip, params.city, params.address]
    const db_response = await postgres.query(queries.hubspotMatchSchool(), values)
    return { 'schools': db_response.rows }
  } catch (error) {
    logger.error('hubspotMatchSchool:', error)
    throw serverError
  }
}

export async function getSchoolProfileCompletion(schoolId: number): Promise<CompletedProfileRate> {
  try {
    // Get school, tours and reviews information
    const db_response = await postgres.query(queries.getSchoolRequiredFields(), [schoolId])
    const schoolBasics =  db_response.rows[0]
    const tours = await getSchoolTourTimes(schoolId) as unknown as { week_availability: string }[]

    const fields = Object.entries(schoolBasics)
      .map(([key, value]) => ({ key, value })) as {key: string, value: string|number|boolean}[]

    let completed = 0
    let required = fields.length
    const missingKeys: string[] = []
    const niceToHaveValues: string[] = []

    /** Each valid value from school object that is required will add to the completed count */
    fields.forEach((item) => {
      if (item.value !== null && item.value) {
        return completed++
      }
      missingKeys.push(item.key)
    })
    const missingValues = mapCategory(missingKeys)

    /** If at least one value from tourAvailable is valid will add to the completed and required count */
    if (tours) {
      const tourAvailable = tours[0].week_availability?.split('').some((day) => day === '1')
      if (tourAvailable) {
        required++
        completed++
      } else {
        niceToHaveValues.push('tours')
      }
    }

    return {
      completedRate: Math.ceil((completed * 100) / required),
      missingValues,
      niceToHaveValues,
      completed, required
    }

  } catch (error) {
    logger.error('getSchoolProfileCompletion:', error)
    if(error.statusCode === notFoundError.statusCode) throw error
    throw serverError
  }
}

const mapCategory = (values: string[]): CategoryFields[] => {
  const result: CategoryFields[] = []

  REQUIRED_PROFILE_COMPLETION.forEach((option) => {
    const matchingFields = option.fields.filter((field) => values.includes(field))

    if (matchingFields.length > 0) {
      result.push({
        category: option.category,
        fields: matchingFields,
      })
    }
  })

  return result
}

export async function getCampaignsStatsBySchoolId(school_id: number, startDate: string, endDate: string) {
  try {
    const db_result = await postgres.query(queries.getCampaignsStatsBySchoolId(), [school_id, startDate, endDate])
    return db_result.rows
  } catch (error) {
    logger.error('getCampaignsStatsBySchoolId:', error)
    throw serverError
  }
}

export async function getCampaignsStatsById(school_id: number, marketing_campaign_id: number) {
  try {
    const db_result = await postgres.query(queries.getCampaignsStatsById(), [school_id, marketing_campaign_id])
    return db_result.rows
  } catch (error) {
    logger.error('getCampaignsStatsById:', error)
    throw serverError
  }
}
