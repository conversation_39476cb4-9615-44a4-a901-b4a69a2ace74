import moment from 'moment'
import { addUserNotification } from './user-notifications'
import { postgres } from '@connectors/postgres'
import { notFoundError, serverError } from '@errors'
import { validate, buildUpdate, getObjectValues, insertLogChange, applicationSetting } from '@utils'
import { insertQuery } from '@queries'
import * as schemas from '@schemas'
import { LeadAssignment, LeadAssignmentParams, LeadAssignmentSearchParams, AnalyticsParams, ManagementLeadAssignmentSearchParams, Top3Params, AssignmentCounts } from '../interfaces/lead-assignments'
import { DEFAULT_SCHOOL_CS } from '@constants'

async function buildFilters(params: LeadAssignmentSearchParams) {
  const filters: string[] = []
  const values: unknown[] = []
  let idx = 1
  let pivotJoin =''
  if (params.assigned_schools === true) {
    if (params.school_id) {
      pivotJoin = `l.id IN (SELECT id FROM leads WHERE school_id = ${params.school_id}) `
    } else {
      // Primero obtener las escuelas asignadas al usuario
      const assignedSchools = await postgres.query(
        `SELECT school_id FROM school_assignments WHERE status='active' AND user_id = $1`,
        [params.user_id]
      )

      if (assignedSchools.rows.length > 0) {
        const schoolIds = assignedSchools.rows.map(row => row.school_id)
        const placeholders = schoolIds.join(',')

        // Ajustar la condición para leads de escuelas asignadas
        pivotJoin = `l.id IN (SELECT id FROM leads WHERE school_id IN (${placeholders})) OR `
      }
      pivotJoin += `l.id in (SELECT lead_id from lead_assignments la WHERE la.status = 'active' AND la.assignment_type = 'school_assigned' AND la.user_id = $${idx})`
      values.push(params.user_id)
      idx++
    }
  } else {
    // Only user_id is related to lead_assignments (la), rest are for view_leads (l)
    pivotJoin = `l.id in (SELECT lead_id from lead_assignments la WHERE la.status = 'active'`
    if (params.user_id && params.user_id !== 'all') {
      pivotJoin += ` AND la.user_id = $${idx}`
      values.push(params.user_id)
      idx++
    }
    pivotJoin += ')'
  }

  if (pivotJoin.includes('OR')) {
    filters.push(`(${pivotJoin})`)
  } else {
    filters.push(pivotJoin)
  }

  // Filters for view_leads (l)
  if (params.status) {
    switch (params.status) {
    case 'new':
      // consider empty and null
      filters.push(`l.status = 'new' and (la.sub_status = '' or la.sub_status IS NULL)`)
      break
    case 'attempting-contact':
    case 'working':
    case 'matched':
      filters.push(`l.status = 'new' and la.sub_status = $${idx}`)
      values.push(params.status)
      idx++
      break
    default:
      filters.push(`l.status = $${idx}`)
      values.push(params.status)
      idx++
      break
    }
  }

  if (params.createdAt_from) {
    filters.push(`l.created_at >= $${idx}`)
    values.push(params.createdAt_from)
    idx++
  }
  if (params.createdAt_to) {
    filters.push(`l.created_at <= $${idx}`)
    values.push(params.createdAt_to)
    idx++
  }
  if (params.grades && params.grades.length > 0) {
    filters.push(`l.grade = ANY($${idx})`)
    values.push(params.grades)
    idx++
  }
  if (params.sources && params.sources.length > 0) {
    filters.push(`CAST(l.lead_source_id AS TEXT) = ANY($${idx})`)
    values.push(params.sources)
    idx++
  }
  if (params.search_notes) {
    filters.push(`l.note ILIKE $${idx}`)
    values.push(`%${params.search_notes}%`)
    idx++
  }
  if (params.year) {
    filters.push(`l.year = $${idx}`)
    values.push(params.year)
    idx++
  }
  if (params.language) {
    filters.push(`l.language = $${idx}`)
    values.push(params.language)
    idx++
  }
  if (params.lead_source_id) {
    filters.push(`l.lead_source_id = $${idx}`)
    values.push(params.lead_source_id)
    idx++
  }
  if (params.lead_status_id) {
    filters.push(`l.lead_status_id = $${idx}`)
    values.push(params.lead_status_id)
    idx++
  }
  if (params.sync_intg_status) {
    filters.push(`l.int_app_id = $${idx}`)
    values.push(params.sync_intg_status)
    idx++
  }
  if (params.assigned_schools === false) {
    filters.push(`la.assignment_type = 'general'`)
  }
  if (params.textSearch) {
    const textSearch = params.textSearch.trim()
    const phoneSearch = textSearch.replace(/[^0-9]/g, '')
    const textFields = [
      'l.parent_first_name',
      'l.parent_last_name',
      'l.child_first_name',
      'l.child_last_name',
      'l.email',
      'l.address',
      'l.zipcode',
      'l.id::TEXT'
    ]
    const searchClause = textFields.map(f => `${f} ILIKE $${idx}`).join(' OR ')
    values.push(`%${textSearch}%`)
    let clause = `(${searchClause}`
    if (phoneSearch.length > 0) {
      clause += ` OR regexp_replace(l.phone, '[^0-9]', '', 'g') ILIKE $${idx + 1}`
      values.push(`%${phoneSearch}%`)
      idx += 2
    } else {
      idx++
    }
    clause += ')'
    filters.push(clause)
  }

  return { filters, values, idx }
}

/**
 * Get all lead assignments for a user
 */
export async function getLeadAssignments(params: LeadAssignmentSearchParams) {
  //await validate(params, schemas.get_by_user_id)
  try {
    const { filters, values, idx } = await buildFilters(params)

    const whereClause = 'WHERE COALESCE(l.deleted,false)=false' + (filters.length > 0 ? ' AND ' + filters.join(' AND ') : '')

    const valuesCount = [...values]

    // Sorting
    const sortField = params.fieldSort || 'l.created_at'
    const sortDirection = params.fieldDirection && ['ASC', 'DESC'].includes(params.fieldDirection.toUpperCase()) ? params.fieldDirection.toUpperCase() : 'DESC'
    const orderBy = `ORDER BY ${sortField} ${sortDirection}`

    // Pagination
    const page = parseInt(params.page || '1', 10)
    const pageSize = parseInt(params.pageSize || '20', 10)
    const offset = (page - 1) * pageSize
    const limitClause = `LIMIT $${idx} OFFSET $${idx + 1}`
    values.push(pageSize, offset)

    const query = `
      SELECT l.id,
          l.status,
          la.id as lead_assignment_id,
          la.sub_status,
          l.school_id,
          s.name as school_name,
          l.parent_first_name,
          l.parent_last_name,
          l.user_id,
          l.email,
          l.phone,
          l.preferred_contact,
          l.child_first_name,
          l.child_last_name,
          l.grade,
          l.child_birthdate,
          l.status,
          l.parent_relationship,
          l.created_at,
          COALESCE(
          (
            Select
              case
                when n.updated_at > l.updated_at then n.updated_at
                else l.updated_at
              end updated_at
            From
              Notes n
            Where
              n.object_type = 'lead'
              and n.object_id = l.id
            order by
              n.id desc
            limit
              1
          ), l.updated_at
          ) as updated_at,
          l.year,
          l.year_accepted,
          coalesce(l.language,'english') as language,
          l.lead_source_id,
          l.scholamatch_url,
          l.sync_intg_status,
          l.address,
          l.address_description,
          l.city,
          l.state,
          l.zipcode,
          l.lead_status_id,
          l.custom_field_1,
          l.custom_field_2,
          l.custom_field_3,
          l.custom_field_4,
          l.custom_field_5,
          CASE
            when not app.id is null then true
            else coalesce(l.application_received, false)
          END application_received,
          coalesce(app.created_at,l.application_received_at) application_received_at,
          coalesce(l.application_valid, true) application_valid,
          coalesce(l.enrollment_confirmed, false) enrollment_confirmed,
          l.enrollment_confirmed_at,
          lstatus.name as lead_status,
          Case
            when l.lead_source_id = -1 then 'ScholaMatch'
            when l.lead_source_id = -2 then 'GeneralLead'
            when l.lead_source_id = -3 then 'ScholaMatch2'
            when l.lead_source_id = -4 then 'Schola School Campaign'
            when l.lead_source_id = -5 then 'ScholaMatch3'
            when l.lead_source_id = -6 then 'PotentialLeadsRO'
            when l.lead_source_id = -7 then 'AutoScholaMatch'
            when l.lead_source_id = -8 then 'Schola Pre-Qualify'
            when l.lead_source_id = -9 then 'Scholamatch SPOT'
            when l.lead_source_id = -10 then 'Schola Profile A'
            when l.lead_source_id = -11 then 'Schola General Campaign - ASM'
            else lsource.name
          End    lead_source,
          la.user_id as user_id_assigned,
          up.email as email_assigned,
          Case
            when l.reason_id is NULL AND l.reason_other is not null then  -1
            else l.reason_id
          End reason_id,
          COALESCE(r.name, l.reason_other) reason
      FROM view_leads l
      LEFT JOIN lead_sources lsource on lsource.id = l.lead_source_id
      LEFT JOIN schools s on l.school_id=s.id
      LEFT JOIN lead_assignments la on la.lead_id = l.id AND la.status = 'active' 
      LEFT JOIN user_profiles up ON up.user_id = la.user_id
      LEFT JOIN lead_statuses lstatus on lstatus.id = l.lead_status_id
      LEFT JOIN reasons r on l.reason_id = r.id
      LEFT JOIN completed_applications app on l.application_id = app.id
      ${whereClause}
      ${orderBy}
      ${limitClause}
    `

    const queryCount = `
      SELECT count(*) as total
      FROM view_leads l
      LEFT JOIN lead_sources lsource on lsource.id = l.lead_source_id
      LEFT JOIN schools s on l.school_id=s.id
      LEFT JOIN lead_assignments la on la.lead_id = l.id AND la.status = 'active'
      ${whereClause}
    `

    //console.log(query, values)
    const db_response = await postgres.query(query, values)
    const db_response_count = await postgres.query(queryCount, valuesCount)

    return {
      result: db_response.rows,
      totalCount: Number(db_response_count.rows[0].total)
    }
  } catch (error) {
    console.error('getLeadAssignmentsByUserIdError:', error)
    throw serverError
  }
}

export async function getCountsByStatus(params: LeadAssignmentSearchParams) {
  try {
    const { filters, values } = await buildFilters(params)

    const whereClause = 'WHERE COALESCE(l.deleted,false)=false' + (filters.length > 0 ? ' AND ' + filters.join(' AND ') : '')

    const query = `
      SELECT
        CASE
          WHEN l.status = 'new' AND (la.sub_status = '' OR la.sub_status IS NULL) THEN 'new'
          WHEN l.status = 'new' AND la.sub_status = 'attempting-contact' THEN 'attempting-contact'
          WHEN l.status = 'new' AND la.sub_status = 'working' THEN 'working'
          WHEN l.status = 'new' AND la.sub_status = 'matched' THEN 'matched'
          ELSE l.status
      END as status_group,
      COUNT(*) as count
      FROM view_leads l
      LEFT JOIN lead_sources lsource on lsource.id = l.lead_source_id
      LEFT JOIN schools s on l.school_id=s.id
      LEFT JOIN lead_assignments la on la.lead_id = l.id AND la.status = 'active'
      LEFT JOIN user_profiles up ON up.user_id = la.user_id
      LEFT JOIN lead_statuses lstatus on lstatus.id = l.lead_status_id
      ${whereClause}
      GROUP BY status_group
      ORDER BY status_group
    `

    const db_response = await postgres.query(query, values)

    const items: Record<string, number> = {}
    db_response.rows.forEach((row) => {
      items[row.status_group] = Number(row.count) || 0
    })
    return items
  } catch (error) {
    console.error('getCountsByStatus:', error)
    throw serverError
  }
}

/**
 * Get active general leads for manager view
 */
export async function getLeadsByManager(params: ManagementLeadAssignmentSearchParams) {
  const { assignedStatus, status, schoolId } = params
  try {
    let idx = 1
    const filters = []
    const values = []

    if (status) {
      if (
        ['attempting-contact', 'working', 'matched'].includes(status)
      ) {
        filters.push(`l.status = 'new' and la.sub_status = $${idx}`)
      } else {
        filters.push(`l.status = $${idx}`)
      }
      values.push(status)
      idx++
    }

    if (schoolId) {
      filters.push(`l.school_id = $${idx}`)
      values.push(schoolId)
      idx++
    }

    if (assignedStatus === 'assigned') {
      filters.push(`la.user_id IS NOT NULL`)
    } else if (assignedStatus === 'unassigned') {
      filters.push('la.user_id IS NULL')
    }

    const whereClause = filters.length > 0 ? 'WHERE ' + filters.join(' AND ') : ''

    const valuesCount = [...values]

    // Pagination
    const page = params.page || 1
    const pageSize = params.pageSize || 20
    const offset = (page - 1) * pageSize
    const limitClause = `LIMIT $${idx} OFFSET $${idx + 1}`
    values.push(pageSize, offset)

    const query = `
      SELECT l.id, la.id as assignment_id, l.parent_first_name, l.parent_last_name, l.school_id, l.grade, l.email, l.phone, l.created_at,
      la.user_id as user_id_assigned,
      CONCAT(up.first_name, ' ', up.last_name) as assigned_to,
      l.status, la.sub_status
      FROM leads l
      LEFT JOIN lead_assignments la ON la.lead_id = l.id  and la.status = 'active'
      LEFT JOIN user_profiles up ON up.user_id = la.user_id
      ${whereClause}
      ORDER BY l.created_at DESC
      ${limitClause}
    `

    const queryCount = `
      SELECT count(*) as total
      FROM leads l
      LEFT JOIN lead_assignments la ON la.lead_id = l.id  and la.status = 'active'
      ${whereClause}
    `
    const db_response_count = await postgres.query(queryCount, valuesCount)

    const db_response = await postgres.query(query, values)
    return {
      result: db_response.rows,
      totalCount: Number(db_response_count.rows[0].total)
    }
  } catch (error) {
    console.error('getGeneralLeadsError:', error)
    throw serverError
  }
}

/**
 * Get a lead assignment by id and user_id
 */
export async function getLeadAssignmentById(id: number): Promise<LeadAssignment | undefined> {
  try {
    const db_response = await postgres.query('SELECT * FROM lead_assignments WHERE id = $1', [
      id,
    ])
    return db_response.rows[0] as LeadAssignment | undefined
  } catch (error) {
    console.error('getLeadAssignmentByIdError:', error)
    throw serverError
  }
}

/**
 * Get general and school_assigned counts by user
*/
export async function getAssignmentCountByUser(user_id: string): Promise<AssignmentCounts> {
  try {
    const query = `
      SELECT count(*) as total
      FROM lead_assignments la
      INNER JOIN leads l ON l.id = la.lead_id
      WHERE
        la.user_id = $1
        AND la.sub_status <> 'matched'
        AND l.status = 'new'
        AND la.status = 'active'
        AND la.assignment_type = $2
    `
    const db_response = await postgres.query(query, [user_id, 'general'])
    const db_response2 = await postgres.query(query, [user_id, 'school_assigned'])

    return {
      general: db_response.rows[0].total,
      assigned: db_response2.rows[0].total
    }

  } catch (error) {
    console.error('getAssignmentCountByUser:', error)
    throw serverError
  }
}

export async function getSchoolsAssignedByUser(user_id: string) {
  try {
    const query = `
  SELECT DISTINCT ON (school_id) school_id, name, school_assigned
  FROM (
  SELECT sa.school_id, s.name, true as school_assigned
    FROM school_assignments sa
    JOIN schools s ON s.id = sa.school_id
    WHERE sa.status = 'active' AND sa.user_id = $1
  UNION
  SELECT s.id as school_id, s.name, false as school_assigned
    FROM lead_assignments la
    LEFT JOIN leads l ON l.id = la.lead_id
    LEFT JOIN schools s ON s.id = l.school_id
    WHERE la.assignment_type = 'school_assigned' AND la.user_id = $1
  ) t
    ORDER BY school_id, school_assigned DESC
  `

    const db_response = await postgres.query(query, [user_id])
    return db_response.rows
  } catch (error) {
    console.error('getSchoolAssignedByUser:', error)
    throw serverError
  }
}

/**
 * Add a new lead assignment
 */
export async function addLeadAssignment(params: LeadAssignment, sendNotification: boolean, school_id: number): Promise<LeadAssignment> {
  await validate(params, schemas.add_lead_assignments)
  try {
    params.created_at = moment().toDate()
    params.updated_at = moment().toDate()
    const values = await getObjectValues(params)

    const db_response = await postgres.query(insertQuery('lead_assignments', params), values)
    const newAssignment = db_response.rows[0] as LeadAssignment

    // Log de cambio
    await insertLogChange({
      user_id: newAssignment.user_id,
      table_name: 'lead_assignments',
      row_id: newAssignment.id,
      operation_type: 'add',
      new_values: newAssignment as unknown as Record<string, unknown>,
      created_at: moment().utc(),
      updated_at: moment().utc(),
    })

    if(sendNotification){
    // Create notification for the assigned user, including lead name if provided
      try {
        const leadName = params.lead_name ? `: ${params.lead_name}` : ''
        await addUserNotification({
          user_id: newAssignment.user_id,
          school_id: school_id,
          type: 'lead_assigned',
          priority: 'high',
          message: `You have been assigned a new lead${leadName} (Lead ID: ${newAssignment.lead_id})`,
        })
      } catch (notificationError) {
        console.error('Error creating lead assignment notification:', notificationError)
      }
    }

    return newAssignment
  } catch (error) {
    console.error('addLeadAssignmentError:', error)
    throw serverError
  }
}

/**
 * Update a lead assignment
 */
export async function updateLeadAssignment(params: LeadAssignmentParams): Promise<LeadAssignment> {
  await validate(params, schemas.update_lead_assignments)
  try {
    const condition = { id: params.id }
    const oldAssignment = await getLeadAssignmentById(condition.id)

    delete params.id
    params.updated_at = moment().toDate()
    const built_update = buildUpdate('lead_assignments', condition, params)
    const db_response = await postgres.query(built_update.text, built_update.values)
    if (db_response.rowCount === 0) throw notFoundError
    const updatedAssignment = db_response.rows[0] as LeadAssignment

    // Log change
    await insertLogChange({
      user_id: updatedAssignment.user_id,
      table_name: 'lead_assignments',
      row_id: updatedAssignment.id,
      operation_type: 'edit',
      old_values: oldAssignment as unknown as Record<string, unknown>,
      new_values: updatedAssignment as unknown as Record<string, unknown>,
      created_at: moment().utc(),
      updated_at: moment().utc(),
    })

    return updatedAssignment
  } catch (error) {
    console.error('updateLeadAssignmentError:', error)
    throw serverError
  }
}

/**
 * Get Analytics
 */
export async function getAnalytics(params: AnalyticsParams) {
  try {
    const values: unknown[] = []
    values.push(params.from)
    values.push(params.to)
    values.push(DEFAULT_SCHOOL_CS)
    const filter_unassigned = `where l.status='new' AND coalesce(l.created_on,'')<>'admin' AND l.imported=false AND coalesce(l.deleted,false)=false AND l.created_at>=$1 AND l.created_at<$2 AND l.id NOT IN (select lead_id from lead_assignments where status='active')`
    const db_response = await postgres.query(`
select
(select coalesce(sum(case when coalesce(s.plan_id,'scholabasics')<>'scholabasics' and coalesce(s.plan_id,'scholabasics')<>'' and s.subscription_end>now() then 1 else 0 end),0)
from leads l
left join schools s on l.school_id=s.id
${filter_unassigned}) unassigned_plan,
(select coalesce(sum(case when coalesce(s.plan_id,'scholabasics')='scholabasics' or coalesce(s.plan_id,'scholabasics')='' or s.subscription_end<now() then 1 else 0 end),0)
from leads l
left join schools s on l.school_id=s.id
${filter_unassigned}) unassigned_noplan,
(select coalesce(sum(case when coalesce(s.plan_id,'scholabasics')<>'scholabasics' and coalesce(s.plan_id,'scholabasics')<>'' and s.subscription_end>now() then 1 else 0 end),0)
from leads l
left join schools s on l.school_id=s.id
where l.status='new'and l.created_at>=$1 and l.created_at<$2 and l.id in (select lead_id from lead_assignments where status='active')) assigned_plan,
(select coalesce(sum(case when coalesce(s.plan_id,'scholabasics')='scholabasics' or coalesce(s.plan_id,'scholabasics')='' or s.subscription_end<now() then 1 else 0 end),0)
from leads l
left join schools s on l.school_id=s.id
where l.status='new'and l.created_at>=$1 and l.created_at<$2 and l.id in (select lead_id from lead_assignments where status='active')) assigned_noplan,
(select coalesce(sum(case when coalesce(s.plan_id,'scholabasics')<>'scholabasics' or coalesce(s.plan_id,'scholabasics')<>'' or s.subscription_end>now() then 1 else 0 end),0)
from leads l
left join schools s on l.school_id=s.id
where l.status='new'and l.created_at>=$1 and l.created_at<$2 and l.id in (select lead_id from lead_assignments where status='active' and sub_status='matched' or id in (select la.id from lead_assignments la INNER JOIN leads l ON la.lead_id=l.id AND l.school_id<>$3))) matched_plan,
(select coalesce(sum(case when coalesce(s.plan_id,'scholabasics')='scholabasics' or coalesce(s.plan_id,'scholabasics')='' or s.subscription_end<now() then 1 else 0 end),0)
from leads l
left join schools s on l.school_id=s.id
where l.status='new'and l.created_at>=$1 and l.created_at<$2 and l.id in (select lead_id from lead_assignments where status='active' and sub_status='matched' or id in (select la.id from lead_assignments la INNER JOIN leads l ON la.lead_id=l.id AND l.school_id<>$3))) matched_noplan      
      `, values)
    const row = db_response.rows[0]
    // Convert all values to numbers
    const unassigned_plan = Number(row.unassigned_plan) || 0
    const unassigned_noplan = Number(row.unassigned_noplan) || 0
    const assigned_plan = Number(row.assigned_plan) || 0
    const assigned_noplan = Number(row.assigned_noplan) || 0
    const matched_plan = Number(row.matched_plan) || 0
    const matched_noplan = Number(row.matched_noplan) || 0

    const total = unassigned_plan + unassigned_noplan + assigned_plan + assigned_noplan
    const round2 = (num: number) => Number.isFinite(num) ? Math.round(num * 100) / 100 : 0

    return {
      unassigned_plan,
      unassigned_noplan,
      assigned_plan,
      assigned_noplan,
      matched_plan,
      matched_noplan,
      total,
      conversion_plan: round2((matched_plan / (assigned_plan || 1)) * 100),
      conversion_noplan: round2((matched_noplan / (assigned_noplan || 1)) * 100),
      conversion_total: round2(((matched_plan + matched_noplan) / ((assigned_plan + assigned_noplan) || 1)) * 100)
    }
  } catch (error) {
    console.error('getAnalyticsError:', error)
    throw serverError
  }
}


/**
 * Get Analytics
 */
export async function getUserAnalytics(params: AnalyticsParams & { user_id?: string }) {
  function isActiveStatus(status: string | undefined): boolean {
    return typeof status === 'string' && status.startsWith('active')
  }

  try {
    const values: unknown[] = []
    values.push(params.from)
    values.push(params.to)
    if (params.user_id) {
      values.push(params.user_id)
    }
    const db_response2 = await postgres.query(`
select l.*, up.email, up.first_name, up.last_name
from log_changes l
inner join user_roles ur on l.user_id=ur.user_id and ur.role_id=2
left join user_profiles up on l.user_id=up.user_id
where table_name='user_profiles' AND l.created_at>=$1 and l.created_at<$2${params.user_id ? ' AND l.user_id = $3' : ''} AND ((old_values::TEXT ilike '%"status": "active%' and new_values::TEXT ilike '%"status": "deactive%') or (new_values::TEXT ilike '%"status": "active%')) order by id      
      `, values)


    const values2: unknown[] = []
    values2.push(params.from)
    values2.push(params.to)
    values2.push(DEFAULT_SCHOOL_CS)
    if (params.user_id) {
      values2.push(params.user_id)
    }
    const db_response = await postgres.query(`
select
(select count(*)
from leads l
where coalesce(deleted, false)=false and l.id in (select lead_id from lead_assignments where status='active' and created_at>=$1 and created_at<$2)) teamassigned,
(select count(*)
from leads l
where coalesce(deleted, false)=false and l.id in (select lead_id from lead_assignments where status='active' and created_at>=$1 and created_at<$2 ${params.user_id ? ' and user_id = $4' : ''})) assigned,
(select count(*)
from leads l
where coalesce(deleted, false)=false and l.id in (select lead_id 
  from lead_assignments 
  where status='active' and created_at>=$1 and created_at<$2 and sub_status='matched'${params.user_id ? ' and user_id = $4' : ''} 
  or id in (select la.id from lead_assignments la INNER JOIN leads l ON la.lead_id=l.id AND l.school_id<>$3${params.user_id ? ' and la.user_id = $4' : ''} 
            where la.status='active' and la.created_at>=$1 and la.created_at<$2))) matched
      `, values2)
    const row = db_response.rows[0]

    const userStatusMap: Record<string, { user_id: string, email: string, first_name: string, last_name: string, activePeriods: Array<{ from: string, to: string }>, totalActiveMs: number }> = {}

    let totalMs = 0
    for (const row of db_response2.rows) {
      const userId = row.user_id
      const oldValues = row.old_values || {}
      const newValues = row.new_values || {}

      // Parse JSON if needed (in case the DB returns as string)
      const oldObj = typeof oldValues === 'string' ? JSON.parse(oldValues) : oldValues
      const newObj = typeof newValues === 'string' ? JSON.parse(newValues) : newValues

      const oldStatus = oldObj.status
      const newStatus = newObj.status
      const newUpdatedAt = newObj.updated_at || row.created_at

      if (!userStatusMap[userId]) {
        userStatusMap[userId] = {
          user_id: userId,
          email: row.email,
          first_name: row.first_name,
          last_name: row.last_name,
          activePeriods: [],
          totalActiveMs: 0
        }
      }

      // Detect transitions
      if (!isActiveStatus(oldStatus) && isActiveStatus(newStatus)) {
        userStatusMap[userId].activePeriods.push({ from: newUpdatedAt, to: '' })
      }
      if (isActiveStatus(oldStatus) && !isActiveStatus(newStatus)) {
        const periods = userStatusMap[userId].activePeriods
        if (periods.length > 0 && !periods[periods.length - 1].to) {
          periods[periods.length - 1].to = newUpdatedAt
          const from = new Date(periods[periods.length - 1].from).getTime()
          const to = new Date(newUpdatedAt).getTime()
          if (!isNaN(from) && !isNaN(to)) {
            userStatusMap[userId].totalActiveMs += Math.max(0, to - from)
            totalMs += Math.max(0, to - from)
          }
        }
      }
    }

    for (const userId in userStatusMap) {
      const periods = userStatusMap[userId].activePeriods
      if (periods.length > 0 && !periods[periods.length - 1].to) {
        periods[periods.length - 1].to = new Date().toISOString()
        const from = new Date(periods[periods.length - 1].from).getTime()
        const to = Date.now()
        if (!isNaN(from) && !isNaN(to)) {
          userStatusMap[userId].totalActiveMs += Math.max(0, to - from)
          totalMs += Math.max(0, to - from)
        }
      }
    }

    const userStatusTimes = Object.values(userStatusMap)
    console.log(row)
    return { teamAssigned: Number(row.teamassigned || 0),  assigned: Number(row.assigned || 0), matched: Number(row.matched || 0), activeTimeMs: totalMs, userStatusTimes }
  } catch (error) {
    console.error('getUserAnalyticsError:', error)
    throw serverError
  }
}
/**
 * Get Team Status
 */
export async function getTeamBalanceStatus() {
  try {
    const db_response = await postgres.query(`
select up.user_id, up.first_name, up.last_name, up.email , coalesce(up.status, 'inactive') status, la.total
from user_profiles up
inner join user_roles ur on ur.user_id = up.user_id and ur.role_id=2 and deleted=false
left join (select la.user_id, count(la.*) total from lead_assignments la left join leads l on la.lead_id=l.id where la.status='active' and l.status='new' group by la.user_id) la on up.user_id=la.user_id
order by up.first_name, up.last_name      
      `)
    return db_response.rows
  } catch (error) {
    console.error('getTeamBalanceStatusError:', error)
    throw serverError
  }
}
/**
 * Get Top 3 schools based on scholamtch url
 */
export async function getTop3Scholamatch(params: Top3Params) {
  try {
    const values: unknown[] = []
    values.push(params.grade)
    values.push(params.zip)
    values.push(params.features)
    values.push(params.school_types)
    const db_response = await postgres.query(`
select ss.position, ss.school_id,s.name as school_name, s.address
from search_history_schools ss 
LEFT JOIN schools s on ss.school_id=s.id
where search_history_id in
(SELECT sh.id
FROM search_history sh
LEFT JOIN search_features_history sf on sh.id=sf.search_history_id
WHERE grade=$1 and zip=$2 and sh.school_types=$4
group by sh.id, sh.grade, sh.zip
HAVING array_agg(DISTINCT sf.feature_id ORDER BY sf.feature_id) = (
	SELECT array_agg(DISTINCT features ORDER BY features)
	FROM unnest($3::int[]) AS features
)
order by sh.id desc
limit 1)
order by position
limit 3
      `, values)
    return db_response.rows
  } catch (error) {
    console.error('getTop3ScholamatchError:', error)
    throw serverError
  }
}


/**
 * run assign process, leads not assigned
 */
export async function assign() {
  try {
    const settings = await applicationSetting.getQueueSettings()
    await applicationSetting.insertOrUpdate({ name: 'queue-assign-execution', value: moment().utc().format('YYYY-MM-DD HH:mm:ss')})
    if(!settings['queue-status']) return { assigned: 0, message: 'Queue Inactive' }

    // 1. Get leads pending assignment (no record in lead_assignments, status 'new')
    // Now also select email and phone for grouping
    const leadsResult = await postgres.query(`
      SELECT l.id, l.school_id, l.parent_first_name, l.parent_last_name, l.email, l.phone
      FROM leads l
      LEFT JOIN lead_assignments la ON la.lead_id = l.id AND la.status = 'active'
      WHERE la.id IS NULL AND l.status = 'new' AND l.created_at> '2025-03-01' AND coalesce(l.created_on,'')<>'admin' AND l.imported=false AND coalesce(l.deleted,false)=false
      ORDER BY id
    `)
    const leadsToAssign = leadsResult.rows.map((row: { id: number, school_id: number | null, parent_first_name: string, parent_last_name: string, email: string, phone: string }) => ({
      id: row.id,
      school_id: row.school_id,
      lead_name: `${row.parent_first_name} ${row.parent_last_name}`.trim(),
      email: row.email,
      phone: row.phone
    }))
    if (leadsToAssign.length === 0) return { assigned: 0, message: 'No leads to assign' }

    const maxLeadsPerUser = settings['queue-active-leads'] !== undefined ? parseInt(String(settings['queue-active-leads']), 10) : undefined
    return await assignLeadsToUsers(leadsToAssign, maxLeadsPerUser)
  } catch (error) {
    console.error('assignLeadsError:', error)
    throw serverError
  }
}

export async function reassign() {
  try {
    const settings = await applicationSetting.getQueueSettings()
    if(settings['queue-reassignment-time'] && settings['queue-reassign-execution']){
      const lastExecution = moment(settings['queue-reassign-execution'])
      const reassignmentTime = settings['queue-reassignment-time']
      const nextAllowedExecution = lastExecution.clone().add(reassignmentTime, 'hours')
      if (moment().isBefore(nextAllowedExecution)) {
        return { assigned: 0, message: 'Reassignment not due yet' }
      }
    }
    await applicationSetting.insertOrUpdate({ name: 'queue-reassign-execution', value: moment().utc().format('YYYY-MM-DD HH:mm:ss')})
    if(!settings['queue-status']) return { assigned: 0, message: 'Queue Inactive' }
    if(!settings['queue-automatic-balancing']) return { assigned: 0, message: 'Lead Balancing Inactive' }

    // 1. Get leads currently assigned but still with lead.status='new', lead_assignment.sub_status='' and passed the configured hours since assignment
    const reassignmentHours = parseInt(String(settings['queue-reassignment-time'] ?? '0'))
    if (!reassignmentHours || reassignmentHours <= 0) return { assigned: 0, message: 'Reassignment time not configured' }

    // Now also select email and phone for grouping
    const leadsResult = await postgres.query(`
      SELECT l.id, l.school_id, la.user_id, la.id as lead_assigment_id, l.parent_first_name, l.parent_last_name, l.email, l.phone
      FROM leads l
      INNER JOIN lead_assignments la ON la.lead_id = l.id AND la.status = 'active' AND la.sub_status = ''
      WHERE l.status = 'new' AND coalesce(l.deleted,false)=false
        AND (la.updated_at <= NOW() - INTERVAL '${reassignmentHours} hours')
    `)
    const leadsToAssign = leadsResult.rows.map((row: { id: number, school_id: number, user_id: string, lead_assigment_id: string, parent_first_name: string, parent_last_name: string, email: string, phone: string }) => ({
      id: row.id,
      school_id: row.school_id,
      user_id: row.user_id,
      lead_assigment_id: row.lead_assigment_id,
      lead_name: `${row.parent_first_name} ${row.parent_last_name}`.trim(),
      email: row.email,
      phone: row.phone
    }))
    if (leadsToAssign.length === 0) return { assigned: 0, message: 'No leads to reassign' }

    const maxLeadsPerUser = settings['queue-active-leads'] !== undefined ? parseInt(String(settings['queue-active-leads']), 10) : undefined
    return await assignLeadsToUsers(leadsToAssign, maxLeadsPerUser)
  } catch (error) {
    console.error('assignLeadsError:', error)
    throw serverError
  }
}


// Assignment logic extracted for internal reuse
// Helper: Get active users, Team users, and school assignments
async function getActiveUsersAndAssignments() {
  const usersResult = await postgres.query(`
    SELECT up.user_id, (CASE WHEN ur.user_id IS NOT NULL THEN true ELSE false END) as is_team
    FROM user_profiles up
    INNER JOIN user_roles ur ON up.user_id = ur.user_id AND ur.role_id = 2
    WHERE up.status ilike 'active%' and up.status<>'active|only_assigned' and up.deleted=false
  `)
  const users = usersResult.rows.map((row: { user_id: string }) => row.user_id)
  const teamUserIds = usersResult.rows.filter((row: { is_team: boolean }) => row.is_team).map((row: { user_id: string }) => row.user_id)
  let schoolAssignments: Array<{ user_id: string, school_id: number }> = []
  if (teamUserIds.length > 0) {
    const assignmentsResult = await postgres.query(`
      SELECT user_id, school_id FROM school_assignments WHERE status='active' AND user_id = ANY($1)
    `, [teamUserIds])
    schoolAssignments = assignmentsResult.rows.map((row: { user_id: string, school_id: number }) => ({ user_id: row.user_id, school_id: row.school_id }))
  }
  return { users, schoolAssignments }
}

// Helper: Get assignments map (user_id -> count)
async function getAssignmentsMap(): Promise<Record<string, number>> {
  const assignmentsResult = await postgres.query(`
    SELECT la.user_id, COUNT(la.*) as count
    FROM lead_assignments la
    INNER JOIN leads l on l.id=la.lead_id AND l.status='new' AND coalesce(la.sub_status,'')<>'matched'
    WHERE la.status = 'active'
    GROUP BY la.user_id
  `)
  const assignmentsMap: Record<string, number> = {}
  assignmentsResult.rows.forEach((row: { user_id: string, count: string }) => {
    assignmentsMap[row.user_id] = parseInt(row.count, 10)
  })
  return assignmentsMap
}

// Helper: Sort users by load
function getSortedUsersByLoad(users: string[], assignmentsMap: Record<string, number>): string[] {
  return [...users].sort((a, b) => {
    const countA = assignmentsMap[a] || 0
    const countB = assignmentsMap[b] || 0
    return countA - countB
  })
}

// Helper: Save queue audit log
async function saveQueueAuditLog(queueAuditLog: any) {
  try {
    await postgres.query(
      `INSERT INTO queue_audit (created_at, user_id, log) VALUES (now(), $1, $2)`,
      ['system', JSON.stringify(queueAuditLog)]
    )
  } catch (e) {
    console.error('Error saving queue_audit:', e)
  }
}

export async function assignLeadsToUsers(
  leadsToAssign: Array<{ id: number, school_id: number, user_id?: string, lead_assigment_id?: string, lead_name?: string, email?: string, phone?: string }>,
  maxLeadsPerUser?: number
) {
  // 1. Get users and assignments
  const { users, schoolAssignments } = await getActiveUsersAndAssignments()
  if (users.length === 0) return { assigned: 0, message: 'No active users found' }
  // 2. Get assignments map
  const assignmentsMap = await getAssignmentsMap()
  // 3. Save initial assignments per user
  const initialAssignments: Record<string, number> = {}
  users.forEach(user_id => {
    initialAssignments[user_id] = assignmentsMap[user_id] || 0
  })

  // 4. Group leads by unique person (by email/phone)
  const personMap: Record<string, Array<typeof leadsToAssign[0]>> = {}
  const getPersonKey = (lead: typeof leadsToAssign[0]) => {
    // Combine lead_name, email, and phone for unique person key
    const name = (lead.lead_name || '').trim().toLowerCase()
    const email = (lead.email || '').trim().toLowerCase()
    const phone = (lead.phone || '').trim()
    return `name:${name}|email:${email}|phone:${phone}`
  }
  for (const lead of leadsToAssign) {
    const key = getPersonKey(lead)
    if (!personMap[key]) personMap[key] = []
    personMap[key].push(lead)
  }
  //console.log('personMap', personMap)
  // 5. Assign leads by person, ensuring all leads of the same person go to the same user (even if exceeding max)
  let assignedCount = 0
  const assignmentDetails: Array<{ lead_id: number, assigned_user_id: string }> = []
  const usersInvolved = new Set<string>()
  const leadsInvolved = new Set<number>()
  const unassignedLeads: Array<typeof leadsToAssign[0]> = []

  // Track which user is assigned to each person key
  const personUserMap: Record<string, string> = {}

  for (const [personKey, personLeads] of Object.entries(personMap)) {
    // Try to find an existing assignment for this person (by email/phone) in current assignments
    let assignedUserId: string | undefined
    // Try to find a user who already has a lead with this email/phone
    // Check if this personKey has already been assigned to a user in this batch
    if (personUserMap[personKey]) {
      assignedUserId = personUserMap[personKey]
    }
    //console.log('assignedUserId', assignedUserId )

    // If not found, assign as usual (by load, but can exceed max for person grouping)
    if (!assignedUserId) {
      // Find available users (not at max, unless this is a person grouping)
      const availableUsers = users.filter(u =>
        typeof maxLeadsPerUser === 'number' ? (assignmentsMap[u] || 0) < maxLeadsPerUser : true
      )
      //console.log('availableUsers', maxLeadsPerUser,availableUsers)
      if (availableUsers.length === 0) {
        for (const lead of personLeads) {
          unassignedLeads.push(lead)
        }
        continue
      }
      // Sort by load
      const availableSortedUsers = getSortedUsersByLoad(availableUsers, assignmentsMap)
      //console.log('availableSortedUsers', availableSortedUsers)
      assignedUserId = availableSortedUsers[0]
    }

    // Assign all leads of this person to the same user
    personUserMap[personKey] = assignedUserId
    //console.log('personUserMap', personUserMap)

    for (const lead of personLeads) {
      // Use the normal assignOrReassignLead logic, but force assignedUserId and allow exceeding max for this scenario
      let assignment_type = 'general'
      // If the lead has a school_id and there is a CTeam user assigned to that school, assign directly
      if (lead.school_id) {
        const found = schoolAssignments.find(a => a.school_id === lead.school_id && (!maxLeadsPerUser || (assignmentsMap[a.user_id] || 0) < maxLeadsPerUser || a.user_id === assignedUserId))
        if (found) {
          assignedUserId = found.user_id
          assignment_type = 'school_assigned'
        }
      }
      // If this is a reassignment (lead_assigment_id present)
      if (lead.lead_assigment_id) {
        if (lead.user_id && lead.user_id !== assignedUserId) {
          // console.log('1-updateLeadAssignment',{
          //   id: Number(lead.lead_assigment_id),
          //   user_id: lead.user_id,
          //   status: 'reassigned',
          // })
          // console.log('1-addLeadAssignment',{
          //   user_id: assignedUserId,
          //   lead_id: lead.id,
          //   status: 'active',
          //   sub_status: '',
          //   assignment_type,
          //   assignment_method: 'automatic'
          // })
          await updateLeadAssignment({
            id: Number(lead.lead_assigment_id),
            user_id: lead.user_id,
            status: 'reassigned',
          })
          await addLeadAssignment({
            user_id: assignedUserId,
            lead_id: lead.id,
            status: 'active',
            sub_status: '',
            assignment_type,
            assignment_method: 'automatic'
          }, true, lead.school_id)
          assignmentsMap[assignedUserId] = (assignmentsMap[assignedUserId] || 0) + 1
          try {
            await addUserNotification({
              user_id: lead.user_id,
              type: 'lead_reassigned',
              priority: 'medium',
              message: `A lead${lead.lead_name ? ` (${lead.lead_name})` : ''} (Lead ID: ${lead.id}) has been reassigned to another user.`,
            })
          } catch (notificationError) {
            console.error('Error creating lead reassignment notification:', notificationError)
          }
          assignedCount++
          assignmentDetails.push({ lead_id: lead.id, assigned_user_id: assignedUserId })
          usersInvolved.add(assignedUserId)
          leadsInvolved.add(lead.id)
        }
        // If the user does not change, do nothing
      } else {
        // console.log('2-addLeadAssignment',{
        //   user_id: assignedUserId,
        //   lead_id: lead.id,
        //   status: 'active',
        //   sub_status: '',
        //   assignment_type,
        //   assignment_method: 'automatic'
        // })
        await addLeadAssignment({
          user_id: assignedUserId,
          lead_id: lead.id,
          status: 'active',
          sub_status: '',
          assignment_type,
          assignment_method: 'automatic'
        }, true, lead.school_id)
        assignmentsMap[assignedUserId] = (assignmentsMap[assignedUserId] || 0) + 1
        assignedCount++
        assignmentDetails.push({ lead_id: lead.id, assigned_user_id: assignedUserId })
        usersInvolved.add(assignedUserId)
        leadsInvolved.add(lead.id)
      }
    }
  }

  // Prepare info of users involved with initial and final assignments
  const usersInfo = Array.from(usersInvolved).map(user_id => ({
    user_id,
    initial_assignments: initialAssignments[user_id] || 0,
    final_assignments: assignmentsMap[user_id] || 0
  }))

  // save on queue_audit
  const queueAuditLog = {
    assigned: assignedCount,
    message: `Assigned ${assignedCount} leads`,
    usersWithoutNewAssignments: users
      .filter(user_id => !usersInvolved.has(user_id))
      .map(user_id => ({
        user_id,
        initial_assignments: initialAssignments[user_id] || 0,
        final_assignments: assignmentsMap[user_id] || 0
      })),
    usersWithNewAssignments: usersInfo,
    leads: Array.from(leadsInvolved),
    assignments: assignmentDetails,
    maxLeadsPerUser,
    unassignedLeads: unassignedLeads.map(l => l.id)
  }
  await saveQueueAuditLog(queueAuditLog)
  return queueAuditLog
}
