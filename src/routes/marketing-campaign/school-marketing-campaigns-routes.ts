import express from 'express'

//Import controller
import {marketingCampaing<PERSON>ontroller as controller} from '@controllers'

//Import interfaces
import {
  ChangeTypeMarketingCampaignRequest,
  GetApplicationUrlForTestMarketingCampaignRequest,
  GetExecutionsByLeadIdMarketingCampaignRequest,
  GetMarketingCampaignByIdRequest,
  GetMarketingCampaignCountsRequest,
  GetMarketingCampaignsRequest,
  GetScholaMarketingCampaignsRequest,
  InsertOrUpdateMarketingCampaignRequest,
  RenameMarketingCampaignRequest,
  RequestAuth,
  SendTestMarketingCampaignRequest,
  SetStatusMarketingCampaignRequest,
  UploadEmailCampaignSchoolLogoRequest,
  GetExecutionsByCampaignIdRequest,
  GetExecutionsByIdRequest
} from '@interfaces'

// Import error files and handlers
import {http_status_codes} from '../../error-handling/http-status-codes'
import { badRequestError } from '@errors'

// Import middleware to validate token
import { validateManager } from '../../middleware/validate-token'

// Import middleware
import { upload } from '../../connectors/s3'
import { converter } from '@utils'

const router = express.Router()

router.route('/:school_id/marketing/campaigns')
  .get(validateManager, async (req: RequestAuth, res) => {
    try {
      const params = req.params as unknown as GetMarketingCampaignsRequest
      const query = req.query as unknown as GetMarketingCampaignsRequest
      const request =  {...params, ...query}

      if(request.school_id) request.school_id = Number(request.school_id)
      if(request.page) request.page = Number(request.page)
      if(request.pageSize) request.pageSize = Number(request.pageSize)
      if(request.archived) request.archived = converter.toBoolean(request.archived)

      if (!request.school_id) {
        throw badRequestError
      }
      const response = await controller.getMarketingCampaigns(request)
      res.status(http_status_codes.OK).send(response)

    } catch (error) {
      console.log(error)
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })
  .post(validateManager, async (req: RequestAuth, res) => {
    try {
      const params = req.params as unknown as GetMarketingCampaignsRequest
      const query = req.body as unknown as GetMarketingCampaignsRequest
      const request =  {...params, ...query}

      if(request.school_id) request.school_id = Number(request.school_id)
      if(request.page) request.page = Number(request.page)
      if(request.pageSize) request.pageSize = Number(request.pageSize)


      if (!request.school_id) {
        throw badRequestError
      }
      const response = await controller.getMarketingCampaigns(request)
      res.status(http_status_codes.OK).send(response)

    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

router.route('/:school_id/marketing/campaign/:campaign_id')
  .get(validateManager, async (req: RequestAuth, res) => {
    try {
      const params = req.params as unknown as GetMarketingCampaignByIdRequest
      const request =  {...params}

      if(request.campaign_id) request.campaign_id = Number(request.campaign_id)
      if(request.school_id) request.school_id = Number(request.school_id)

      if (!request.school_id) {
        throw badRequestError
      }
      const response = await controller.getMarketingCampaignById(request)
      res.status(http_status_codes.OK).send(response)

    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

router.route('/:school_id/marketing/campaign')
  .post(validateManager, async (req: RequestAuth, res) => {
    try {
      const params = req.params as unknown as InsertOrUpdateMarketingCampaignRequest
      const body = req.body as unknown as InsertOrUpdateMarketingCampaignRequest
      const request =  {...params, ...body}

      if(request.id) request.id = Number(request.id)
      if(request.school_id) request.school_id = Number(request.school_id)
      if(request.layout_id) request.layout_id = Number(request.layout_id)
      if(request.marketing_template_id) request.marketing_template_id = Number(request.marketing_template_id)

      if (!request.school_id) {
        throw badRequestError
      }
      const response = await controller.insertOrUpdateMarketingCampaign(request, req.auth?.credentials?.sub)
      res.status(http_status_codes.OK).send(response)

    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

router.route('/:school_id/marketing/campaign/:campaign_id/set-status')
  .post(validateManager, async (req: RequestAuth, res) => {
    try {
      const params = req.params as unknown as SetStatusMarketingCampaignRequest
      const body = req.body as unknown as SetStatusMarketingCampaignRequest
      const request =  {...params, ...body}

      if(request.campaign_id) request.campaign_id = Number(request.campaign_id)
      if(request.school_id) request.school_id = Number(request.school_id)

      if (!request.school_id) {
        throw badRequestError
      }
      const response = await controller.setStatusMarketingCampaign(request, req.auth?.credentials?.sub)
      res.status(http_status_codes.OK).send(response)

    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

router.route('/:school_id/marketing/campaign/:campaign_id/rename')
  .post(validateManager, async (req: RequestAuth, res) => {
    try {
      const params = req.params as unknown as RenameMarketingCampaignRequest
      const body = req.body as unknown as RenameMarketingCampaignRequest
      const request =  {...params, ...body}

      if(request.campaign_id) request.campaign_id = Number(request.campaign_id)
      if(request.school_id) request.school_id = Number(request.school_id)

      if (!request.school_id) {
        throw badRequestError
      }
      const response = await controller.renameMarketingCampaign(request)
      res.status(http_status_codes.OK).send(response)

    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

router.route('/:school_id/marketing/campaign/:campaign_id/change-type')
  .post(validateManager, async (req: RequestAuth, res) => {
    try {
      const params = req.params as unknown as ChangeTypeMarketingCampaignRequest
      const body = req.body as unknown as ChangeTypeMarketingCampaignRequest
      const request =  {...params, ...body}

      if(request.campaign_id) request.campaign_id = Number(request.campaign_id)
      if(request.school_id) request.school_id = Number(request.school_id)

      if (!request.school_id) {
        throw badRequestError
      }
      const response = await controller.changeTypeMarketingCampaign(request)
      res.status(http_status_codes.OK).send(response)

    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

router.route('/:school_id/marketing/schola/campaigns')
  .get(validateManager, async (req: RequestAuth, res) => {
    try {
      const params = req.params as unknown as GetScholaMarketingCampaignsRequest
      const query = req.query as unknown as GetScholaMarketingCampaignsRequest
      const request =  {...params, ...query}

      if(request.school_id) request.school_id = Number(request.school_id)
      if(request.page) request.page = Number(request.page)
      if(request.pageSize) request.pageSize = Number(request.pageSize)

      const response = await controller.getScholaMarketingCampaigns(request)
      res.status(http_status_codes.OK).send(response)

    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

router.route('/:school_id/marketing/tables/count')
  .get(validateManager, async (req: RequestAuth, res) => {
    try {
      const params = req.params as unknown as GetMarketingCampaignCountsRequest
      const query = req.query as unknown as GetMarketingCampaignCountsRequest
      const request =  {...params, ...query}

      if(request.school_id) request.school_id = Number(request.school_id)

      if (!request.school_id) {
        throw badRequestError
      }
      const response = await controller.getMarketingCampaignCounts(request)
      res.status(http_status_codes.OK).send(response)
    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

router.route('/:school_id/marketing/email/campaign/:campaign_id/image/upload')
  .post(validateManager, upload.fields([
    { name: 'file', maxCount: 1 },
    { name: 'original', maxCount: 1 }
  ]), async (req: RequestAuth, res) => {
    try {
      const params = req.params as unknown as UploadEmailCampaignSchoolLogoRequest
      const body = req.body as unknown as UploadEmailCampaignSchoolLogoRequest
      const files = req.files as {[fieldname: string]: Express.Multer.File[]}

      if(files.file) { params.file = files.file[0].buffer }
      if(files.original) { params.original = files.original[0].buffer }
      const request =  {...params, ...body}

      if(request.school_id) request.school_id = Number(request.school_id)
      if(request.campaign_id) request.campaign_id = Number(request.campaign_id)

      const response = await controller.uploadEmailCampaignSchoolLogo(request)
      res.status(http_status_codes.OK).send(response)

    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

router.route('/:school_id/marketing/campaign/send-test')
  .post(async (req: RequestAuth, res) => {
    try {
      const params = req.params as unknown as SendTestMarketingCampaignRequest
      const request =  {...params, lead: req.body.lead, marketingCampaign: req.body.marketingCampaign}  as SendTestMarketingCampaignRequest

      if(request.school_id) request.school_id = Number(request.school_id)
      if(request.marketingCampaign){
        if(request.marketingCampaign.id) request.marketingCampaign.id = Number(request.marketingCampaign.id)
        if(request.marketingCampaign.layout_id) request.marketingCampaign.layout_id = Number(request.marketingCampaign.layout_id)
        if(request.marketingCampaign.marketing_template_id) request.marketingCampaign.marketing_template_id = Number(request.marketingCampaign.marketing_template_id)
        request.marketingCampaign.school_id = request.school_id
      }

      const response = await controller.sendTest(request)
      res.status(http_status_codes.OK).send(response)

    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

router.route('/:school_id/marketing/campaign/get-application-url-for-test')
  .post(async (req: RequestAuth, res) => {
    try {
      const params = req.params as unknown as GetApplicationUrlForTestMarketingCampaignRequest
      const request =  {...params}

      if(request.school_id) request.school_id = Number(request.school_id)

      const response = await controller.getApplicationUrlForTest(request)
      res.status(http_status_codes.OK).send(response)

    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

router.route('/:school_id/marketing/campaign/lead/:lead_id')
  .get(async (req: RequestAuth, res) => {
    try {
      const params = req.params as unknown as GetExecutionsByLeadIdMarketingCampaignRequest
      const request =  {...params}

      if(request.school_id) request.school_id = Number(request.school_id)
      if(request.lead_id) request.lead_id = Number(request.lead_id)

      const response = await controller.getExecutionsByLead(request)
      res.status(http_status_codes.OK).send(response)

    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

router.route('/:school_id/marketing/campaign/:marketing_campaign_id/executions')
  .get(validateManager, async (req: RequestAuth, res) => {
    try {
      const params = req.params as unknown as GetExecutionsByCampaignIdRequest
      const request =  {...params}

      if(request.marketing_campaign_id) request.marketing_campaign_id = Number(request.marketing_campaign_id)
      if(request.school_id) request.school_id = Number(request.school_id)

      if (!request.school_id) {
        throw badRequestError
      }
      const response = await controller.getExecutionsByCampaignId(request)
      res.status(http_status_codes.OK).send(response)

    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })


router.route('/:school_id/marketing/campaign/:marketing_campaign_id/auditstotals')
  .get(validateManager, async (req: RequestAuth, res) => {
    try {
      const params = req.params as unknown as GetExecutionsByCampaignIdRequest
      const request =  {...params}

      if(request.marketing_campaign_id) request.marketing_campaign_id = Number(request.marketing_campaign_id)
      if(request.school_id) request.school_id = Number(request.school_id)

      if (!request.school_id) {
        throw badRequestError
      }
      const response = await controller.getAuditsTotalsByCampaignId(request)
      res.status(http_status_codes.OK).send(response)

    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })


router.route('/:school_id/marketing/campaign/:marketing_campaign_id/audits')
  .get(validateManager, async (req: RequestAuth, res) => {
    try {
      const params = req.params as unknown as GetExecutionsByCampaignIdRequest
      const request =  {...params}

      if(request.marketing_campaign_id) request.marketing_campaign_id = Number(request.marketing_campaign_id)
      if(request.school_id) request.school_id = Number(request.school_id)

      if (!request.school_id) {
        throw badRequestError
      }
      const response = await controller.getAuditsByCampaignId(request)
      res.status(http_status_codes.OK).send(response)

    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })


router.route('/:school_id/marketing/campaign/:marketing_campaign_id/audits-report')
  .get(validateManager, async (req: RequestAuth, res) => {
    try {
      const params = req.params as unknown as GetExecutionsByCampaignIdRequest
      const request =  {...params}

      if(request.marketing_campaign_id) request.marketing_campaign_id = Number(request.marketing_campaign_id)
      if(request.school_id) request.school_id = Number(request.school_id)

      if (!request.school_id) {
        throw badRequestError
      }
      const result = await controller.getAuditsReport(request)
      res.setTimeout(300000).header({
        'Content-Disposition': `attachment; filename=execution-${request.marketing_campaign_id}.csv`,
        'Content-Type': 'text/csv'
      }).status(http_status_codes.OK).send(result)

    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

router.route('/:school_id/marketing/campaign/:marketing_campaign_id/executions/:execution_id')
  .get(validateManager, async (req: RequestAuth, res) => {
    try {
      const params = req.params as unknown as GetExecutionsByIdRequest
      const request =  {...params}

      if(request.execution_id) request.execution_id = Number(request.execution_id)
      if(request.school_id) request.school_id = Number(request.school_id)

      if (!request.school_id) {
        throw badRequestError
      }
      const result = await controller.getExecutionsReport(request)
      res.setTimeout(300000).header({
        'Content-Disposition': `attachment; filename=execution-${request.execution_id}.csv`,
        'Content-Type': 'text/csv'
      }).status(http_status_codes.OK).send(result)

    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

router.route('/:school_id/marketing/campaign/:campaign_id/:node_id')
  .post(validateManager, async (req: RequestAuth, res) => {
    try {
      const params = (req.params as any)
      if (params.campaign_id) params.campaign_id = Number(params.campaign_id)
      if (params.school_id) params.school_id = Number(params.school_id)

      if (!params.campaign_id || !params.school_id || !params.node_id) {
        throw badRequestError
      }
      params.body = req.body

      const response = await controller.callFromDag(params)
      res.status(http_status_codes.OK).send(response)

    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

router.route('/test/campaign_id/:campaign_id/school_id/:school_id/status/:status')
  .post(validateManager, async (req: RequestAuth, res) => {
    try {
      const params = {
        campaign_id: Number(req.params.campaign_id) || 0,
        school_id: Number(req.params.school_id) || 0,
        status: req.params.status || 'Active'
      }
      const response = await controller.managingDags(params)
      res.status(http_status_codes.OK).send(response)

    } catch (error) {
      res.status(error.statusCode || 500).json({
        message: error.message,
        statusCode: error.statusCode
      })
    }
  })

export = router
