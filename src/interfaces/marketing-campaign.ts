import { Lead, School } from '.'
import { Dictionary, Pagination, RequestAuth } from './common'
import {Moment} from 'moment'

export interface GetScholaMarketingCampaignsRequest {
  school_id: number
  page: number
  pageSize: number
}

export interface GetMarketingCampaignsRequest {
  school_id: number
  page?: number
  pageSize?: number
  platform?: string
  archived?: boolean
  sortField?: string
  sortDirection?: string
  filters?: MarketingCampaignFilter[]
}

export interface GetMarketingCampaignsResponse {
    results: GetMarketingCampaignsDBResult[]
    pagination: MarketingCampaignPagination

}

export interface GetMarketingCampaignsDBResult {
  id: number
  name: string
  type: string
  audience_language: string
  status: string
  updated_at: Date
  layout_id: number
  platform: string
  params: Dictionary<string, string>[]
  marketing_template_id: number
}


export interface GetMarketingCampaignByIdRequest {
  school_id: number
  campaign_id: number
}

export interface InsertOrUpdateMarketingCampaignRequest {
  id: number
  school_id: number
  platform: string
  type: string
  name: string
  audience_language: string
  layout_id: number
  marketing_template_id: number
  params: Dictionary<string, string>[]
}

export interface InsertMarketingCampaignParams {
  school_id: number
  created_at: Moment
  updated_at: Moment
  status: string
  platform: string
  type: string
  name: string
  audience_language: string
  layout_id: number
  marketing_template_id: number
  params: string
}

export interface UpdateMarketingCampaignParams {
  id: number
  updated_at: Moment
  platform: string
  type: string
  name: string
  audience_language: string
  layout_id: number
  marketing_template_id: number
  params: string
}


export interface SetStatusMarketingCampaignRequest {
  campaign_id: number
  school_id: number
  status: string
  keep_running?: boolean
}

export interface SetStatusMarketingCampaignParams {
  id: number
  updated_at: Moment
  status: string
  dag_id?: string
}

export interface SetStatusMarketingCampaignResponse {
  success: boolean
}

export interface RenameMarketingCampaignRequest {
  campaign_id: number
  school_id: number
  name: string
}

export interface RenameMarketingCampaignParams {
  id: number
  updated_at: Moment
  name: string
}

export interface RenameMarketingCampaignResponse {
  success: boolean
}

export interface ChangeTypeMarketingCampaignRequest {
  campaign_id: number
  school_id: number
  type: string
}

export interface ChangeTypeMarketingCampaignParams {
  id: number
  updated_at: Moment
  status: string
  type: string
}

export interface ChangeTypeMarketingCampaignResponse {
  success: boolean
}

export interface GetScholaMarketingCampaignsResponse {
  results: ScholaMarketingCampaignReport[]
  pagination: Pagination
}

export interface ScholaMarketingCampaignError {
  error: string
}

export interface ScholaMarketingCampaignReport {
  name?: string
  leads?: number
  cost_per_lead?: number
  application_received_manual?: number
  applications?: number
  potential_revenue?: number
  case_id?: string
  status?: string
  start_date?: string
  created_at?: string
  updated_at?: string
}
export interface TrackViewsRequest {
  request?: RequestAuth
  campaign_id: number
  identifier: string
}
export interface TrackViewsResponse {
  image: Buffer
  content_type: string
}

export interface MarketingCampaignTrackLookupData {
  ip: string
  country: string
  region: string
  city: string
  browser: string
  browser_version: string
  os: string
  os_version: string
  device: string
  device_version: string
  user_agent: string
}

export interface GetMarketingCampaignCountsRequest {
  school_id: number
  ids: string
  filtering: string
}

export interface GetMarketingCampaignCountsResponse {
  campaignBreakdown?: number
  scholaMarketing?: number
  campaignEmail?: number
  campaignSMS?: number
  [prop: string]: number
}

export interface UploadEmailCampaignSchoolLogoRequest {
  school_id: number
  campaign_id: number
  fileName: string
  file?: Buffer
  original?: Buffer
}

export interface UploadEmailCampaignSchoolLogoResponse {
  url: string
}

export interface CheckSchedulesMarketingCampaignResponse {
  success: boolean
  message: string[]
}

export interface SendTestMarketingCampaignRequest {
  school_id: number
  marketingCampaign: MarketingCampaignSendTest
  lead: LeadSendTest
}

export interface SendTestMarketingCampaignResponse {
  success: boolean
  message: string
}

export interface GetApplicationUrlForTestMarketingCampaignRequest {
  school_id: number
}

export interface GetApplicationUrlForTestMarketingCampaignResponse {
  url: string
}
export interface MarketingCampaignSendTest {
  id: number
  school_id: number
  platform: string
  type: string
  name: string
  audience_language: string
  layout_id: number
  marketing_template_id: number
  params: Dictionary<string, string>[]
}

export interface MarketingCampaignTemplate  {
  id: string
  name: string
  description: string
  icon: string
  layout_id: string
  params: Dictionary<string, string>[]
}

export interface GetExecutionsByLeadIdMarketingCampaignRequest {
  school_id: number
  lead_id: number
}

export interface GetExecutionsByLeadIdMarketingCampaignResponse {
  type: string
  created_at: Date
  status: string
  marketing_campaign_id: number
  marketing_campaign_executed_audit_id: number
  campaign: number
}


export interface LeadSendTest {
  email: string
  phone: string
}

export interface MarketingCampaignPagination {
  page: number
  pageCount: number
  pageSize: number
  rowCount: number
}
export interface MarketingCampaignTracker
{
  id: number
  campaign_id: number
  clicks?: number
  views?: number
  viewed_at?: Date
  clicked_at?: Date
  identifier: string
  region: string
  city: string
  browser: string
  browser_version: string
  os: string
  os_version: string
  device: string
  url: string
  url_type?: string
  ip: string
  country: string
  device_version: string
}

export interface InsertOrUpdateMarketingCampaignTrackerParams
{
  identifier: string
  campaign_id?: number
  clicks?: number
  views?: number
  viewed_at?: Moment
  clicked_at?: Moment
  region: string
  city: string
  browser: string
  browser_version: string
  os: string
  os_version: string
  device: string
  url?: string
  ip: string
  country: string
  device_version: string
  url_type?: string
  user_agent?: string
}

export type InsertMarketingCampaignTrackerParams = InsertOrUpdateMarketingCampaignTrackerParams
export interface UpdateMarketingCampaignTrackerParams extends InsertOrUpdateMarketingCampaignTrackerParams {
  id?: number
}

export interface TrackClicksRequest {
  request?: RequestAuth
  campaign_id: number
  identifier: string
  location: string
  url_type: string
}
export interface TrackClicksResponse {
  url_to_redirect_to: string
}


export interface LeadScholaSystem extends Lead {
  application_url: string
}

export interface MarketingCampaignParams {
  key: string
  value: string
}

export interface LastExecutedMarketingCampaign {
  school_id: number
  marketing_campaign_id: number
  last_executed: string
}

export interface MarketingCampaign {
  id?: number
  school_id: number
  created_at: Date
  updated_at?: Date
  name: string
  marketing_template_id?: number
  audience_language: string
  type: string
  layout_id?: number
  params?: MarketingCampaignParam[]
  status: string
  platform: string
  dag_id?: string
}

export interface MarketingCampaignParam {
  key:string
  value:string
}

export interface ScheduleMarketingCampaignShouldBeProcessed {
  success: boolean
  school_id: number
  marketing_campaign_id: number
  executed?: string
  error: ScheduleMarketingCampaignError
}

export interface MarketingCampaignShouldBeProcessedResult {
  success: boolean
  message: string
}

export interface ScheduleMarketingCampaignError {
  message: string
  code: number
}

export interface MarketingCampaignParameter {
  key:string
  name: string
  type: string
  value: string
}

export interface MarketingCampaingSourceData {
  marketing_campaign_lead?: MarketingCampaignLead
  school?: School
  aplication_url?: string
}

export interface MarketingCampaignExecutedAudit {
  id: number
  school_id: number
  marketing_campaign_id :number
  params: string
  reference: string
  status: number
  error: string
  executed: string
  created_at: Date
  updated_at?: Date
}
export interface InsertMarketingCampaignExecutedAudit {
  school_id: number
  marketing_campaign_id :number
  params: string
  reference?: string
  status: number
  error: string
  executed: string
  created_at: Moment
  updated_at?: Moment
}

export interface MarketingCampaignResult<T> {
  success: boolean
  message: string
  result?: T | undefined
}

export interface MarketingCampaignLayoutResult {
  masterLayoutContent: string
  layoutContent: string
}

export interface MarketingCampaignRenderResult {
  content: string
  params?: MarketingCampaignParam[]
  trackerIdentifier?: string
}
export interface MarketingCampaignLead {
  id: number
  parent_first_name: string
  parent_last_name: string
  child_first_name: string
  child_last_name: string
  grade: string
  country: string
  city: string
  state: string
  zipcode: string
  status: string
  created_on: string
  created_at: string
  language: string
  lead_status_id: number
  school_id: number
  email: string
  phone: string
  lead_source_id: number
  messages_count: number
  reason_id: number
  reason_other: string
  scholamatch_url: string
}

export interface MarketingCampaignFilter {
  name: string
  value: string
}

export interface MarketingNode {
  id: string
  type: string
  isFirstNode: boolean
  data?: MarketingCampaignParam[]
}

export interface MyRequestPayload {
  schoolId: string
  config: {
    lead: string
  }
  args: {
    owner: string
    depends_on_past: boolean
  }
  params: {
    dag_id: string
    description: string
    tags: any[]
    dag_display_name: string
  }
  tasks: {
    nodes: any[]
    edges: any[]
  };
}

export interface GetExecutionsByCampaignIdRequest {
  school_id: number
  marketing_campaign_id: number
}

export interface GetExecutionsByCampaignIdResponse {
  type: string
  created_at: Date
  status: string
  marketing_campaign_id: number
  marketing_campaign_executed_audit_id: number
  campaign: number
}

export interface GetEmailAuditRequest {
  message_id: string
}

export interface GetAuditResponse {
  id: number
  created_at: Date
  status: string
  error: string
  marketing_campaign_id: number
  marketing_campaign_executed_audit_id: number
  lead_id: number
}

export interface GetExecutionsByIdRequest {
  school_id: number
  execution_id: number
}
