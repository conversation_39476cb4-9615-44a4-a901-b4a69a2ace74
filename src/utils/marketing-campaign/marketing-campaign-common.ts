import moment, { Moment } from 'moment'
import postgres from '../../connectors/postgres'
import geoip from 'geoip-lite'
import uaParserJs from 'ua-parser-js'

//Import interfaces
import {
  UpdateShortUrl,
  MarketingCampaign,
  GetMarketingCampaignsResponse,
  LastExecutedMarketingCampaign,
  MarketingCampaignExecutedAudit,
  InsertMarketingCampaignExecutedAudit,
  School,
  GetMarketingCampaignsDBResult,
  Dictionary,
  InsertMarketingCampaignParams,
  UpdateMarketingCampaignParams,
  SetStatusMarketingCampaignParams,
  RenameMarketingCampaignParams,
  ChangeTypeMarketingCampaignParams,
  RequestAuth,
  MarketingCampaignTrackLookupData,
  MarketingCampaignTracker,
  InsertOrUpdateMarketingCampaignTrackerParams,
  InsertMarketingCampaignTrackerParams,
  UpdateMarketingCampaignTrackerParams,
  GetExecutionsByLeadIdMarketingCampaignResponse,
  MarketingCampaignLead,
  MarketingCampaignFilter,
  GetExecutionsByCampaignIdResponse,
  GetAuditResponse
} from '../../interfaces'

//queries
import {
  insertQuery,
  marketing_campaigns_queries,
} from '../../queries'

//error-handling
import {
  serverError
} from '../../error-handling'

//Import utils
import {
  buildUpdate,
  getObjectValues,
  shortUrls
} from '..'

//Import constants
import {
  MARKETING_CAMPAIGN_CONSTANTS,
  SCHOLA_TABLES
} from '../../constants'
import { NumericDictionary } from 'lodash'

/**
 * Gets the marketing campaigns by some filters like schoolId, platform and with pagination
 * @param school_id school's id
 * @param platform platform
 * @param page number of page
 * @param page_size page size
 * @returns {Promise<GetMarketingCampaignsResponse>}
 */
export async function getMarketingCampaigns(school_id: number, platform: string, archived: boolean, page: number, page_size: number, sortField: string, sortDirection: string, filters: Array<MarketingCampaignFilter>=[]): Promise<GetMarketingCampaignsResponse>{
  const result: GetMarketingCampaignsResponse = {
    results:[],
    pagination: {
      page: page,
      pageSize: page_size,
      pageCount: 0,
      rowCount: 0
    }
  }
  try {
    //get the count
    const page_rows = page_size
    const page_offset = (Number(page) - Number(1)) * Number(page_rows)
    result.pagination.rowCount = await getMarketingCampaignsCount(school_id, platform, archived, filters)
    //check if the count is greater than 0
    if(result.pagination.rowCount === 0){
      return result
    }
    //get the rows
    result.pagination.pageCount = Math.ceil(result.pagination.rowCount / page_rows)
    const dbResult = await postgres.query(marketing_campaigns_queries.getMarketingCampaignsByFiltersAndPagination(archived, sortField, sortDirection, filters),[school_id, platform, page_rows, page_offset])
    if(!dbResult) return result
    if(dbResult.rows.length > 0){
      result.results =  dbResult.rows as GetMarketingCampaignsDBResult[]
    }
  } catch (error) {
    console.error('marketing-campaign-common.getMarketingCampaigns() > error', error)
    throw serverError
  }
  return result
}

/**
 * Gets the marketing campaigns count by some filters like schoolId, platform
 * @param school_id school's id
 * @param platform platform
 * @param page number of page
 * @returns {Promise<number>}
 */
export async function getMarketingCampaignsCount(school_id: number, platform: string, archived: boolean, filters: Array<MarketingCampaignFilter> = []): Promise<number>{
  let result = 0
  try {
    //get the count
    const query = marketing_campaigns_queries.getMarketingCampaignsByFiltersAndPaginationCount(archived, filters)
    const dbResult = await postgres.query(query,[school_id, platform])
    if(!dbResult) return result
    if(dbResult.rows.length > 0){
      result =  Number(dbResult.rows[0].count)
    }
  } catch (error) {
    console.error('marketing-campaign-common.getMarketingCampaignsCount() > error', error)
    throw serverError
  }
  return result
}

/**
 * Gets the marketing campaign by id
 * @param marketing_campaign_id
 * @returns Promise<MarketingCampaign>
 */
export async function getMarketingCampaignById(marketing_campaign_id: number): Promise<MarketingCampaign> {
  let result : MarketingCampaign = undefined
  try {
    const db_result = await postgres.query(marketing_campaigns_queries.getMarketingCampaignById(),[marketing_campaign_id])
    if(!db_result) return result
    if(db_result.rows.length > 0){
      result =  db_result.rows[0] as MarketingCampaign
    }
  } catch (error) {
    console.error('marketing-campaign-common.getMarketingCampaignById() > error', error)
    throw serverError
  }
  return result
}

/**
 * Gets the active marketing campaigns for triggers
 * @param school_id school' id
 * @returns {Promise<MarketingCampaign[]>}
 */
//RENAMED FROM getActiveMarketingCampaignBySchoolId
export async function getActiveMarketingCampaignForTriggers(school_id: number): Promise<MarketingCampaign[]> {
  let result : MarketingCampaign[] = []
  try {
    const db_result = await postgres.query(marketing_campaigns_queries.getActiveMarketingCampaignForTriggers(),[school_id])
    if(!db_result) return result
    if(db_result.rows.length > 0){
      result =  db_result.rows as MarketingCampaign[]
    }
  } catch (error) {
    console.error('marketing-campaign-common.getActiveMarketingCampaignForTriggers() > error', error)
    throw serverError
  }
  return result
}

/**
 * Gets all active marketing campaigns for schedules
 * @returns {Promise<MarketingCampaign[]} return a list of marketing campaign
 */
//RENAMED FROM getActiveMarketingCampaigns
export async function getActiveMarketingCampaignForSchedules(): Promise<MarketingCampaign[]> {
  let result : MarketingCampaign[] = []
  try {
    const db_result = await postgres.query(marketing_campaigns_queries.getActiveMarketingCampaignForSchedules())
    if(!db_result) return result
    if(db_result.rows.length > 0){
      result =  db_result.rows as MarketingCampaign[]
    }
  } catch (error) {
    console.error('marketing-campaign-common.getActiveMarketingCampaignForSchedules() > error', error)
    throw serverError
  }
  return result
}

/**
 * Gets active marketing campaign by id
 * @param marketing_campaign_id marketing campaign's id
 * @returns {Promise<MarketingCampaign>} return a object of marketing campaign
 */
export async function getActiveMarketingCampaignById(marketing_campaign_id: number): Promise<MarketingCampaign> {
  let result : MarketingCampaign
  try {
    const db_result = await postgres.query(marketing_campaigns_queries.getActiveMarketingCampaignById(),[marketing_campaign_id])
    if(!db_result) return result
    if(db_result.rows.length > 0){
      result =  db_result.rows[0]
    }
  } catch (error) {
    console.error('marketing-campaign-common.getActiveMarketingCampaignById() > error', error)
    throw serverError
  }
  return result
}

/**
 * Gets a list of the last execution of each marketing camapign
 * @returns {Promise<LastExecutedMarketingCampaign[]>}
 */
export async function getLastExecutedMarketingCampaigns(): Promise<LastExecutedMarketingCampaign[]> {
  let result : LastExecutedMarketingCampaign[] = []
  try {
    const db_result = await postgres.query(marketing_campaigns_queries.getLastExecutedMarketingCampaigns(),[])
    if(!db_result) return result
    if(db_result.rows.length > 0){
      result = db_result.rows as LastExecutedMarketingCampaign[]
    }
  } catch (error) {
    console.error('marketing-campaign-common.getLastExecutedMarketingCampaigns() > error', error)
    throw serverError
  }
  return result
}

/**
 * Sets the status to marketing campaign
 * @param {number} markering_campaign_id marketing campaign's id
 * @param {string} marketing_campaign_status marketing campaign's status
 */
export async function setCampaignStatus(markering_campaign_id: number, marketing_campaign_status: string): Promise<void> {
  try {
    const condition = {id: markering_campaign_id }
    const update_params = {
      status: marketing_campaign_status,
      updated_at: moment.utc()
    }
    const built_update = buildUpdate(SCHOLA_TABLES.MARKETING_CAMPAIGNS, condition, update_params)
    await postgres.query(built_update.text, built_update.values)
  } catch (error) {
    console.error('application-setting.setCampaignStatus() > error', error)
    throw serverError
  }
}

/**
 * Gets a school by id
 * @param {number} school_id school's id
 * @returns {Promise<School>} return a object of school
 */
export async function getSchoolById(school_id: number): Promise<School> {
  let result : School | undefined = undefined
  try {
    const db_result = await postgres.query(marketing_campaigns_queries.getSchoolById(),[school_id])
    if(!db_result) return result
    if(db_result.rows.length > 0){
      result = db_result.rows[0]
    }
  } catch (error) {
    console.error('marketing-campaign-common.getSchoolById() > error', error)
    throw serverError
  }
  return result
}

/**
 * Gets a list of leads by school's id and lead's status
 * @param {number} school_id school's id
 * @param {string|undefined} status lead's status
 * @param {string|undefined} language lead's language
 * @param {number|undefined} lead_status_id lead's lead status id
 * @returns {Promise<MarketingCampaignLead[]>} return a list of leads
 */
// RENAMED FROM (getLeadsBySchoolIdAndStatus and getLeadsBySchoolId)
type FilteringParamsType = { key: string, value: string | boolean }[]

export async function getLeadsForSchedules(school_id: number, language: string|undefined, filteringParams: FilteringParamsType): Promise<MarketingCampaignLead[]> {
  let result : MarketingCampaignLead[] = []

  try {
    const findFilter = (field: string, type?: 'number' | 'boolean') => {
      const filter = filteringParams.find(({ key }: { key:string }) => key === field)
      if (filter) {
        if (type === 'number' && typeof filter.value === 'string') {
          return filter.value==='' ? [] : filter.value.split(',').map(Number)
        }

        if (type === 'boolean') {
          return filter.value === 'true' ? true : false
        }
        return filter.value
      }
    }
    const statusValue = findFilter('status') as string
    const finalStatus =  statusValue === MARKETING_CAMPAIGN_CONSTANTS.SENT_TO_ALL ? undefined : statusValue

    const gradeValue = findFilter('grade')
    const sub_stage = findFilter('lead_status_id', 'number')
    const sources = findFilter('lead_source_id', 'number')
    const year = findFilter('year') as string
    const finalYear = !year ? undefined : year.split(',').map(item => {
      if(item === '') return 'no-value'
      return item
    }).join(',')
    const enrollment_confirmed = findFilter('enrollment_confirmed', 'boolean')
    const application_received = findFilter('application_received', 'boolean')
    const sync_intg_status = findFilter('sync_intg_status')
    const intg_campus = findFilter('intg_campus')

    const from_date = (finalStatus.includes('archived') ? moment().add(-1,'years').format('YYYY-MM-DD')+'T01:01:01' : undefined)

    const db_result = await postgres.query(marketing_campaigns_queries.getLeadsForSchedules(),[
      school_id,
      finalStatus,
      language,
      sub_stage,
      from_date,
      gradeValue,
      finalYear,
      sources,
      enrollment_confirmed,
      application_received,
      sync_intg_status,
      intg_campus
    ])

    if(!db_result) return result
    if(db_result.rows.length > 0){
      result = db_result.rows
    }
  } catch (error) {
    console.error('marketing-campaign-common.getLeadsForSchedules() > error', error)
    throw serverError
  }
  return result
}

/**
 * Gets a list of leads with today's birthdays
 * @param {number} school_id school's id
 * @returns {Promise<MarketingCampaignLead[]>} return a list of leads
 */
export async function getLeadsBirthdaysToday(school_id: number, language: string): Promise<MarketingCampaignLead[]> {
  let result : MarketingCampaignLead[] = []
  try {
    const db_result = await postgres.query(marketing_campaigns_queries.getLeadsBirthdaysToday(),[school_id, language])
    if(!db_result) return result
    if(db_result.rows.length > 0){
      result = db_result.rows
    }
  } catch (error) {
    console.error('marketing-campaign-common.getLeadsBirthdaysToday() > error', error)
    throw serverError
  }
  return result
}

/**
 * Inserts marketing campaign execute audit
 * @param {InsertMarketingCampaignExecutedAudit} params
 * @param {number} params.school_id
 * @param {number} params.marketing_campaign_id
 * @param {string} params.params
 * @param {string} params.reference
 * @param {number} params.status
 * @param {string} params.error
 * @param {string} params.executed
 * @param {Moment} params.created_at
 * @param {Moment} params.updated_at
 * @returns Promise<MarketingCampaignExecutedAudit> return a object of marketing campaign execute audit
 */
export async function insertMarketingCampaignExecutedAudit(params: InsertMarketingCampaignExecutedAudit): Promise<MarketingCampaignExecutedAudit>{
  let result: MarketingCampaignExecutedAudit =  undefined
  try {
    if(!params.created_at) params.created_at = moment.utc()
    if(!params.updated_at) params.updated_at = moment.utc()

    const params_values = await getObjectValues(params)
    const insert_result = await postgres.query(insertQuery(SCHOLA_TABLES.MARKETING_CAMPAIGN_EXECUTED_AUDITS, params),params_values)

    if(insert_result.rows.length > 0){
      result = insert_result.rows[0] as MarketingCampaignExecutedAudit
    }
  } catch (error) {
    console.error('marketing-campaign-common.insertMarketingCampaignExecutedAudit() > error', error)
    throw serverError
  }

  return result
}

/**
 * Check if a marketing campaign exists
 * @param {number} marketing_campaign_id
 * @returns {Promise<boolean>} returns a boolean value
 */
export async function existsMarketingCampaign(marketing_campaign_id: number): Promise<boolean>{
  try {
    const marketing_campaign = await getMarketingCampaignById(marketing_campaign_id)
    if(marketing_campaign){
      return true
    }
  } catch (error) {
    console.error('marketing-campaign-common.insertMarketingCampaignExecutedAudit() > error', error)
    throw serverError
  }
  return false
}
/**
 * Inserts a marketing campaign
 * @param {InsertMarketingCampaignParams} params
 * @param {number} params.school_id
 * @param {Moment} params.created_at
 * @param {string} params.status
 * @param {string} params.platform
 * @param {string} params.type
 * @param {string} params.name
 * @param {string} params.audience_language
 * @param {number} params.layout_id
 * @param {number} params.marketing_template_id
 * @param {string} params.params
 * @returns {Promise<MarketingCampaign>} returns a object of marketing campaign
 */
export async function insertMarketingCampaign(params: InsertMarketingCampaignParams): Promise<MarketingCampaign>{
  let result: MarketingCampaign = undefined
  try {
    params.created_at = moment.utc()
    const params_values = await getObjectValues(params)
    const insert_result = await postgres.query(insertQuery(SCHOLA_TABLES.MARKETING_CAMPAIGNS, params), params_values)
    if(insert_result.rows.length > 0){
      result = insert_result.rows[0] as MarketingCampaign
    }
  } catch (error) {
    console.error('marketing-campaign-common.insertMarketingCampaign() > error', error)
    throw serverError
  }
  return result
}
/**
 * Updates a marketing campaign
 * @param {UpdateMarketingCampaignParams} params
 * @param {number} params.id
 * @param {Moment} params.updated_at
 * @param {string} params.platform
 * @param {string} params.type
 * @param {string} params.name
 * @param {string} params.audience_language
 * @param {number} params.layout_id
 * @param {number} params.marketing_template_id
 * @param {string} params.params
 * @returns {Promise<MarketingCampaign>} returns a object of marketing campaign
 */
export async function updateMarketingCampaign(params: UpdateMarketingCampaignParams): Promise<MarketingCampaign>{
  let result: MarketingCampaign =  undefined
  try {
    params.updated_at = moment.utc()
    const condition = {id: params.id }
    delete params.id
    const built_update = buildUpdate(SCHOLA_TABLES.MARKETING_CAMPAIGNS, condition, params)
    const db_result = await postgres.query(built_update.text, built_update.values)
    if(db_result.rows.length > 0){
      result = db_result.rows[0] as MarketingCampaign
    }
  } catch (error) {
    console.error('application-setting.updateMarketingCampaign() > error', error)
    throw serverError
  }

  return result
}

/**
 * Updates the url on marketing campaign's params
 * @param {Dictionary<string,string>[]} params
 * @returns {Dictionary<string,string>[]} returns a dictionary array
 */
export async function updateUrlsOnMarketingCampaignParams(params: Dictionary<string,string>[]): Promise<Dictionary<string,string>[]>{
  const param_custom_url = params.find(p=> p.key === MARKETING_CAMPAIGN_CONSTANTS.PARAMETERS.CTA_CUSTOM_URL_KEY)
  if(param_custom_url && param_custom_url.value !== ''){
    let longUrl = param_custom_url.value
    const protocol = getParamterValueFromParams(MARKETING_CAMPAIGN_CONSTANTS.PARAMETERS.CTA_CUSTOM_PROTO_KEY, params)||'http://'
    if(longUrl.toLowerCase().startsWith('http') === false){
      longUrl =`${protocol}${longUrl}`
    }

    const param_short_url = params.find(p=> p.key === MARKETING_CAMPAIGN_CONSTANTS.PARAMETERS.CTA_CUSTOM_SHORT_URL_UUID_KEY)
    if(!param_short_url){
      const short_url_result = await shortUrls.createShortURL(longUrl)
      params.push({
        key: MARKETING_CAMPAIGN_CONSTANTS.PARAMETERS.CTA_CUSTOM_SHORT_URL_UUID_KEY,
        value: short_url_result.uuid
      })
      return params
    }
    else{
      const shortUrlResult = await shortUrls.getLongURLByUUID(param_short_url.value)
      if(shortUrlResult){
        const update_short_url: UpdateShortUrl = {
          id: shortUrlResult.id,
          url: longUrl
        }
        await shortUrls.updateLongURL(update_short_url)
        return params
      }
      else{
        const shortUrlResult = await shortUrls.createShortURL(longUrl)
        param_short_url.value = shortUrlResult.uuid
        return params
      }
    }
  }
  else{
    return params
  }
}

/**
 * Sets the status to a marketing campaign
 * @param {SetStatusMarketingCampaignParams} params
 * @param {number} params.id
 * @param {Moment} params.updated_at
 * @param {string} params.status
 */
export async function setStatusMarketingCampaign(params: SetStatusMarketingCampaignParams): Promise<MarketingCampaign>{
  let result: MarketingCampaign =  undefined
  try {
    params.updated_at = moment.utc()
    const condition = {id: params.id }
    delete params.id
    const built_update = buildUpdate(SCHOLA_TABLES.MARKETING_CAMPAIGNS, condition, params)
    const db_result =await postgres.query(built_update.text, built_update.values)
    if(db_result.rows.length > 0){
      result = db_result.rows[0] as MarketingCampaign
    }
    return result
  } catch (error) {
    console.error('application-setting.setStatusMarketingCampaign() > error', error)
    throw serverError
  }
}

/**
 * Rename to a marketing campaign
 * @param {RenameMarketingCampaignParams} params
 * @param {number} params.id
 * @param {Moment} params.updated_at
 * @param {string} params.name
 */
export async function renameMarketingCampaign(params: RenameMarketingCampaignParams): Promise<void>{
  try {
    params.updated_at = moment.utc()
    const condition = {id: params.id }
    delete params.id
    const built_update = buildUpdate(SCHOLA_TABLES.MARKETING_CAMPAIGNS, condition, params)
    await postgres.query(built_update.text, built_update.values)
  } catch (error) {
    console.error('application-setting.renameMarketingCampaign() > error', error)
    throw serverError
  }
}

/**
 * Change type to a marketing campaign
 * @param {ChangeTypeMarketingCampaignParams} params
 * @param {number} params.id
 * @param {Moment} params.updated_at
 * @param {string} params.status
 * @param {string} params.type
 */
export async function changeTypeMarketingCampaign(params: ChangeTypeMarketingCampaignParams): Promise<void>{
  try {
    params.updated_at = moment.utc()
    const condition = {id: params.id }
    delete params.id
    const built_update = buildUpdate(SCHOLA_TABLES.MARKETING_CAMPAIGNS, condition, params)
    await postgres.query(built_update.text, built_update.values)
  } catch (error) {
    console.error('application-setting.changeTypeMarketingCampaign() > error', error)
    throw serverError
  }
}

/**
 * Gets the value from params by key
 * @param {string} key
 * @param {Dictionary<string,string>[]} params
 * @returns {string} returns the value
 */
function getParamterValueFromParams(key: string, params: Dictionary<string,string>[]): string {
  if(params){
    const found =  params.find(x=> x.key === key)
    if(found){
      return found.value
    }
  }
  return ''
}

/**
 * Gets lookup data from the request
 * @param {RequestAuth} request
 * @returns {MarketingCampaignTrackLookupData} returns the marketing campaign track lookup data
 */
export function getLookupData (request: RequestAuth): MarketingCampaignTrackLookupData {
  const lookup_data : MarketingCampaignTrackLookupData = {
    ip: 'Unknown',
    country: 'Unknown',
    region: 'Unknown',
    city: 'Unknown',
    browser: 'Unknown',
    browser_version: 'Unknown',
    os: 'Unknown',
    os_version: 'Unknown',
    device: 'Unknown',
    device_version: 'Unknown',
    user_agent: request.headers['user-agent'] || 'Unknown'
  }
  try {
    let clientIp = ''
    const x_forwarded_for = request.headers['x-forwarded-for']
    if(x_forwarded_for){
      if(Array.isArray(x_forwarded_for) && x_forwarded_for.length > 0){
        clientIp = x_forwarded_for[0].split(',')[0]
      }
      else{
        clientIp = (x_forwarded_for as unknown as string).split(',')[0]
      }
      if(!clientIp){
        clientIp = request.socket.remoteAddress
      }
    }

    lookup_data.ip = clientIp || 'Unknown'

    try {
      const geo = geoip.lookup(clientIp)
      if(geo && geo !== null) {
        lookup_data.country = geo.country
        lookup_data.region = geo.region
        lookup_data.city = geo.city
      }
    } catch (error) {
      console.error('application-setting.getLookupData() > error on read geoip', error)
    }

    try {
      const userAgent = request.headers['user-agent']
      const ua = uaParserJs(userAgent)

      if (ua) {
        lookup_data.browser = ua.browser.name
        lookup_data.browser_version = ua.browser.version
        lookup_data.os = ua.os.name
        lookup_data.os_version = ua.os.version
        lookup_data.device = ua.device.vendor
        lookup_data.device_version = ua.device.model
      }
    } catch (error) {
      console.error('application-setting.getLookupData() > error on read user-agent', error)
    }
  } catch (error) {
    console.error('application-setting.getLookupData() > error ', error)
  }
  return lookup_data
}

/**
 * Inserts or updates a marketing campaign tracker
 * @param {InsertMarketingCampaignTrackerParams} params
 * @param {string} params.identifier
 * @param {number} params.campaign_id
 * @param {number} params.clicks
 * @param {number} params.views
 * @param {Moment} params.viewed_at
 * @param {Moment} params.clicked_at
 * @param {string} params.region
 * @param {string} params.city
 * @param {string} params.browser
 * @param {string} params.browser_version
 * @param {string} params.os
 * @param {string} params.os_version
 * @param {string} params.device
 * @param {string} params.url
 * @param {string} params.ip
 * @param {string} params.country
 * @param {string} params.device_version
 * @returns {Promise<MarketingCampaignTracker>}
 */
export async function insertOrUpdateMarketingCampaignTracker(params: InsertOrUpdateMarketingCampaignTrackerParams): Promise<MarketingCampaignTracker> {
  let result : MarketingCampaignTracker = undefined
  try {
    const marketing_campaign_tracker = await getMarketingCampaignTrackerByIdentifier(params.identifier)
    if(!marketing_campaign_tracker){
      //insert
      const insert_params = (params as unknown as InsertMarketingCampaignTrackerParams)
      if(insert_params.views > 0){
        insert_params.viewed_at = moment.utc()
      }
      if(insert_params.clicks > 0 ){
        insert_params.clicked_at = moment.utc()
      }
      result = await insertMarketingCampaignTracker(insert_params)
    }
    else{
      //update
      const update_params = (params as unknown as UpdateMarketingCampaignTrackerParams)
      update_params.id = marketing_campaign_tracker.id
      if(update_params.views > 0 && !marketing_campaign_tracker.viewed_at){
        update_params.viewed_at = moment.utc()
      }
      if(update_params.clicks > 0 && !marketing_campaign_tracker.clicked_at){
        update_params.clicked_at = moment.utc()
      }
      update_params.views = (update_params.views || 0) + (marketing_campaign_tracker.views || 0)
      update_params.clicks = (update_params.clicks || 0) + (marketing_campaign_tracker.clicks || 0)
      result = await updateMarketingCampaignTracker(update_params)
    }

  } catch (error) {
    console.error('marketing-campaign-common.insertOrUpdateMarketingCampaignTracker() > error', error)
    throw serverError
  }

  return result
}

/**
 * Inserts a marketing campaign tracker
 * @param {InsertMarketingCampaignTrackerParams} params
 * @param {string} params.identifier
 * @param {number} params.campaign_id
 * @param {number} params.clicks
 * @param {number} params.views
 * @param {Moment} params.viewed_at
 * @param {Moment} params.clicked_at
 * @param {string} params.region
 * @param {string} params.city
 * @param {string} params.browser
 * @param {string} params.browser_version
 * @param {string} params.os
 * @param {string} params.os_version
 * @param {string} params.device
 * @param {string} params.url
 * @param {string} params.ip
 * @param {string} params.country
 * @param {string} params.device_version
 * @returns {Promise<MarketingCampaignTracker>}
 */
export async function insertMarketingCampaignTracker(params: InsertMarketingCampaignTrackerParams): Promise<MarketingCampaignTracker> {
  let result : MarketingCampaignTracker = undefined
  try {
    const params_values = await getObjectValues(params)
    const insert_result = await postgres.query(insertQuery(SCHOLA_TABLES.MARKETING_CAMPAIGN_TRACKER, params), params_values)
    if(insert_result.rows.length > 0){
      result = insert_result.rows[0] as MarketingCampaignTracker
    }
  } catch (error) {
    console.error('marketing-campaign-common.insertMarketingCampaignTracker() > error', error)
    throw serverError
  }

  return result
}

/**
 * Updates a marketing campaign tracker
 * @param {UpdateMarketingCampaignTrackerParams} params
 * @param {number} params.id
 * @param {string} params.identifier
 * @param {number} params.campaign_id
 * @param {number} params.clicks
 * @param {number} params.views
 * @param {Moment} params.viewed_at
 * @param {Moment} params.clicked_at
 * @param {string} params.region
 * @param {string} params.city
 * @param {string} params.browser
 * @param {string} params.browser_version
 * @param {string} params.os
 * @param {string} params.os_version
 * @param {string} params.device
 * @param {string} params.url
 * @param {string} params.ip
 * @param {string} params.country
 * @param {string} params.device_version
 * @returns {Promise<MarketingCampaignTracker>}
 */
export async function updateMarketingCampaignTracker(params: UpdateMarketingCampaignTrackerParams): Promise<MarketingCampaignTracker> {
  let result : MarketingCampaignTracker = undefined
  try {
    const condition = {id: params.id }
    delete params.id
    const built_update = buildUpdate(SCHOLA_TABLES.MARKETING_CAMPAIGN_TRACKER, condition, params)
    const db_result = await postgres.query(built_update.text, built_update.values)
    if(db_result.rows.length > 0){
      result = db_result.rows[0] as MarketingCampaignTracker
    }
  } catch (error) {
    console.error('marketing-campaign-common.updateMarketingCampaignTracker() > error', error)
    throw serverError
  }

  return result
}

/**
 * Gets a marketing campaign tracker by identifier
 * @param {string} identifier
 * @returns {Promise<MarketingCampaignTracker>}
 */
export async function getMarketingCampaignTrackerByIdentifier(identifier: string): Promise<MarketingCampaignTracker> {
  let result : MarketingCampaignTracker = undefined
  try {
    const db_result = await postgres.query(marketing_campaigns_queries.getMarketingCampaignTrackerByIdentifier(),[identifier])
    if(!db_result) return result
    if(db_result.rows.length > 0){
      result =  db_result.rows[0] as MarketingCampaignTracker
    }
  } catch (error) {
    console.error('marketing-campaign-common.getMarketingCampaignTrackerByIdentifier() > error', error)
    throw serverError
  }
  return result
}

/**
 * Gets a list of marketing campaigns executions by school's id and lead's id
 * @param {number} school_id
 * @param {number} lead_id
 * @returns {Promise<GetExecutionsByLeadIdMarketingCampaignResponse[]>}
 */
export async function getExecutionsByLead(school_id: number, lead_id: number): Promise<GetExecutionsByLeadIdMarketingCampaignResponse[]> {
  let result: GetExecutionsByLeadIdMarketingCampaignResponse[] = []
  try {
    const db_result = await postgres.query(marketing_campaigns_queries.getExecutionsByLead(),[school_id, lead_id])
    if(!db_result) return result
    if(db_result.rows.length > 0){
      result =  db_result.rows as GetExecutionsByLeadIdMarketingCampaignResponse[]
    }
  } catch (error) {
    console.error('marketing-campaign-common.getExecutionsByLead() > error', error)
    throw serverError
  }
  return result
}


/**
 * Gets a list of marketing campaigns executions by school's id and lead's id
 * @param {number} school_id
 * @param {number} marketing_campaign_id
 * @returns {Promise<GetExecutionsByCampaignIdResponse[]>}
 */
export async function getExecutionsByMarketingCampaignId(school_id: number, marketing_campaign_id: number): Promise<GetExecutionsByCampaignIdResponse[]> {
  let result: GetExecutionsByLeadIdMarketingCampaignResponse[] = []
  try {
    const db_result = await postgres.query(marketing_campaigns_queries.getExecutionsByMarketingCampaignId(),[marketing_campaign_id])
    if(!db_result) return result
    if(db_result.rows.length > 0){
      result =  db_result.rows as GetExecutionsByCampaignIdResponse[]
    }
  } catch (error) {
    console.error('marketing-campaign-common.getExecutionsByLead() > error', error)
    throw serverError
  }
  return result
}

/**
 * Gets a list of marketing campaigns executions by school's id and lead's id
 * @param {number} school_id
 * @param {number} marketing_campaign_id
 * @returns {Promise<GetExecutionsByCampaignIdResponse[]>}
 */
export async function getAuditsTotalsByMarketingCampaignId(school_id: number, marketing_campaign_id: number): Promise<GetExecutionsByCampaignIdResponse[]> {
  let result: GetExecutionsByLeadIdMarketingCampaignResponse[] = []
  try {
    const db_result = await postgres.query(marketing_campaigns_queries.getAuditsTotalsByMarketingCampaignId(),[marketing_campaign_id])
    if(!db_result) return result
    if(db_result.rows.length > 0){
      result =  db_result.rows as GetExecutionsByCampaignIdResponse[]
    }
  } catch (error) {
    console.error('marketing-campaign-common.getExecutionsByLead() > error', error)
    throw serverError
  }
  return result
}

/**
 * Gets a list of marketing campaigns executions by school's id and lead's id
 * @param {number} school_id
 * @param {number} marketing_campaign_id
 * @returns {Promise<GetAuditResponse[]>}
 */
export async function getAuditsByMarketingCampaignId(school_id: number, marketing_campaign_id: number): Promise<GetAuditResponse[]> {
  let result: GetAuditResponse[] = []
  try {
    const db_result = await postgres.query(marketing_campaigns_queries.getAuditsByMarketingCampaignId(),[marketing_campaign_id])
    if(!db_result) return result
    if(db_result.rows.length > 0){
      result =  db_result.rows as GetAuditResponse[]
    }
  } catch (error) {
    console.error('marketing-campaign-common.getExecutionsByLead() > error', error)
    throw serverError
  }
  return result
}

/**
 * Gets a email audit by message id
  * @param {string} message_id
 * @returns {Promise<GetAuditResponse[]>}
 */
export async function getEmailAuditByMessageId(message_id: string): Promise<GetAuditResponse[]> {
  let result: GetAuditResponse[] = []
  try {
    const db_result = await postgres.query(marketing_campaigns_queries.getEmailAuditByMessageId(),[ message_id])
    if(!db_result) return result
    if(db_result.rows.length > 0){
      result =  db_result.rows as GetAuditResponse[]
    }
  } catch (error) {
    console.error('marketing-campaign-common.getEmailAuditByMessageId() > error', error)
    throw serverError
  }
  return result
}


/**
 * Sets the status to a email_audits
  * @param {number} params.id
 * @param {string} params.status
 */
export async function setStatusEmailAudit( id: number, status: number, error : string): Promise<void>{
  try {
    const updated_at = moment.utc()
    const condition = {id: id }
    const params: { updated_at: Moment; status: number; error?: string } = { updated_at, status }
    if (error) params.error = error
    const built_update = buildUpdate(SCHOLA_TABLES.EMAIL_AUDITS, condition, params)
    await postgres.query(built_update.text, built_update.values)
  } catch (error) {
    console.error('application-setting.setStatusEmailAudit() > error', error)
    throw serverError
  }
}


/**
 * Gets list of sms / emails
 * @param {number} school_id
 * @param {number} marketing_campaign_executed_audit_id
 * @returns {Promise<GetExecutionsByCampaignIdResponse[]>}
 */
export async function getAuditsByExecutionId(school_id: number, marketing_campaign_executed_audit_id: number): Promise<GetExecutionsByCampaignIdResponse[]> {
  let result: GetExecutionsByLeadIdMarketingCampaignResponse[] = []
  try {
    const db_result = await postgres.query(marketing_campaigns_queries.getAuditsById(),[marketing_campaign_executed_audit_id])
    if(!db_result) return result
    if(db_result.rows.length > 0){
      result =  db_result.rows as GetExecutionsByCampaignIdResponse[]
    }
  } catch (error) {
    console.error('marketing-campaign-common.getExecutionsByLead() > error', error)
    throw serverError
  }
  return result
}
