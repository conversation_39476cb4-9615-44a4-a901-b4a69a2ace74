import moment from 'moment'
import SchoolBusinessDays from '../school-business-days'

//Import utils
import {
  marketingCampaignCommon,
  marketingCampaignParameters,
  marketingCampaignLayout,
  marketingCampaignRender,
  marketingCampaignSender,
  schoolPhoneUtils,
  schoolUtils,
} from '../../utils'
import {
  getApplicationUrl
} from '../send-application-to-lead'

//Import constants
import {
  STATUSES,
  MARKETING_CAMPAIGN_CONSTANTS,
  LEAD_CREATED_ON_OPTIONS
} from '../../constants'

//Import interfaces
import {
  School,
  Lead,
  MarketingCampaign,
  MarketingCampaignShouldBeProcessedResult,
  InsertMarketingCampaignExecutedAudit,
  MarketingCampaignExecutedAudit,
  MarketingCampaingSourceData,
  MarketingCampaignResult,
  MarketingCampaignLayoutResult,
  MarketingCampaignLead,
  MarketingNode
} from '../../interfaces'

import { triggerDag } from '@connectors/airflow'

/**
 * Execute marketing campaign with shedule
 * @param {MarketingCampaign} marketing_campaign
 * @param {string} executed
 * @param {boolean} validate_plan_in_backend
 * @param {boolean} enable_logs
 * @returns {Promise<MarketingCampaignResult<void>>} returns the result of execution
 */
export async function executeSchedule(marketing_campaign: MarketingCampaign, executed: string, validate_plan_in_backend:boolean, enable_logs:boolean): Promise<MarketingCampaignResult<void>> {
  const result: MarketingCampaignResult<void> = {
    success: false,
    message: '',
    result: undefined
  }
  try {

    if(!marketing_campaign){
      if(enable_logs) console.log('marketing-campaign-execute-schedule.executeSchedule() > marketingCampaign is undefined')
      return result
    }
    //check if the campaign should be processed
    const should_be_processed = shouldBeProcessedMarketingCampaign(marketing_campaign)

    if(enable_logs) console.log('marketing-campaign-execute-schedule.executeSchedule() > should_be_processed', marketing_campaign.id, should_be_processed)

    //check if was unsuccessful the should_be_processed
    if(should_be_processed.success === false) {
      result.message = should_be_processed.message
      return result
    }

    //insert execute audit
    const insert_execute_audit = await insertExecutedAudit(marketing_campaign, executed, should_be_processed)

    //get school
    const school = await marketingCampaignCommon.getSchoolById(marketing_campaign.school_id)
    if(!school){
      if(enable_logs) console.log('marketing-campaign-execute-schedule.executeSchedule() > school is undefined')
      result.message =  'school not found'
      return result
    }

    //get school business days
    const school_business_days = schoolUtils.getSchoolBusinessDays(school)

    //check the plan
    if(validate_plan_in_backend === true && school){
      if (!school.plan_id || !school.subscription_end || moment(school.subscription_end).isBefore()){
        result.message = `schoolId:${school.id}, no active plan`
        return result
      }
    }

    //get the school's phone to send the sms if the campaign is sms type
    let fromPhone: string = undefined
    const school_phones = await schoolPhoneUtils.getBySchoolId(school.id)
    if(school_phones){
      if(school_phones &&  school_phones.length > 0){
        fromPhone = school_phones[0].number
      }
    }

    //get the marketing campaign's layout
    const layout = await marketingCampaignLayout.readLayoutByMarketingCampaign(marketing_campaign)
    if(!layout || !layout.success){
      if(enable_logs) console.log('marketing-campaign-execute-schedule.executeSchedule() > layout is undefined')
      result.message = 'error reading the layout'
      return result
    }

    //get the lead
    const leads = await getLeads(marketing_campaign)
    // console.log('leads:', leads)

    if(enable_logs) console.log('marketing-campaign-execute-schedule.executeSchedule() > leads to process (' + marketing_campaign.id + ')', (leads || []).length)

    while(leads.length){
      if(enable_logs){
        console.log('marketing-campaign-schedule-service > processSchedule > Process batch of Max 15 leads', leads.length)
      }
      //process the campaign for each lead
      await Promise.all(
        (leads||[]).splice(0,15).map(async lead => {
          const marketingCampaignCopy = { ...marketing_campaign }
          const _response = await processScheduleForLead(school, school_business_days, insert_execute_audit, marketingCampaignCopy, layout, lead, fromPhone, enable_logs)
          if(enable_logs){
            console.log('marketing-campaign-schedule-service > processSchedule > processSchedule > result ('+ marketing_campaign.id+', '+lead.id+')', _response)
          }
        })
      )
    }
    //check if the campaign is regular mass to finish it
    if(marketing_campaign.type === MARKETING_CAMPAIGN_CONSTANTS.TYPES.REGULAR_MASS){
      if(enable_logs){
        console.log(`marketing-campaign-execute-schedule.executeSchedule() > finished because is ${MARKETING_CAMPAIGN_CONSTANTS.TYPES.REGULAR_MASS}`)
      }
      await marketingCampaignCommon.setCampaignStatus(marketing_campaign.id, MARKETING_CAMPAIGN_CONSTANTS.STATUSES.FINISHED)
    }
    //ADVANCED trigger
    if(marketing_campaign.type === MARKETING_CAMPAIGN_CONSTANTS.TYPES.ADVANCED && (marketing_campaign.dag_id||'')!==''){
      //trigger dag
      await triggerDag(marketing_campaign.dag_id, 0, insert_execute_audit.id)
    }
    result.success = true
  } catch (error) {
    //don't bubble up the error, just write the error on the logs
    console.error('marketing-campaign-execute-schedule.executeSchedule() > error', error)
  }
  return result
}

/**
 * Process the marketing campaign schedule for a lead
 * @param {School} school
 * @param {SchoolBusinessDays} school_business_days
 * @param {MarketingCampaignExecutedAudit} marketing_campaign_executed_audit
 * @param {MarketingCampaign} marketing_campaign
 * @param {MarketingCampaignResult<MarketingCampaignLayoutResult>} layout
 * @param {MarketingCampaignLead} marketing_campaign_lead
 * @param {string} from_phone
 * @param {boolean} enable_logs
 * @returns {Promise<MarketingCampaignResult<void>>} returns the marketing campaign processing result for the lead
 */
export async function  processScheduleForLead(
  school: School,
  school_business_days: SchoolBusinessDays,
  marketing_campaign_executed_audit: MarketingCampaignExecutedAudit,
  marketing_campaign: MarketingCampaign,
  layout: MarketingCampaignResult<MarketingCampaignLayoutResult>,
  marketing_campaign_lead: MarketingCampaignLead,
  from_phone: string,
  enable_logs: boolean
): Promise<MarketingCampaignResult<void>> {
  const result: MarketingCampaignResult<void> = {
    success: false,
    message: '',
    result: undefined
  }
  try {
    const marketing_campaign_executed_audit_id = marketing_campaign_executed_audit?.id || undefined
    const lead = marketing_campaign_lead as unknown as Lead
    const aplication_url = await getApplicationUrl(school, lead, undefined)
    const source_data: MarketingCampaingSourceData = {
      marketing_campaign_lead: marketing_campaign_lead,
      school: school,
      aplication_url: aplication_url,
    }


    const should_be_processed_lead = await shouldBeProcessedlead(school_business_days, marketing_campaign, marketing_campaign_lead)
    if(enable_logs) console.log('marketing-campaign-schedule-service > processScheduleForLead > shouldBeProcessedleadResult (' + marketing_campaign.id + ',' + marketing_campaign_lead.id +')', should_be_processed_lead)

    if(!should_be_processed_lead.success){
      result.message = should_be_processed_lead.message
      return result
    }

    let platform = (marketing_campaign && marketing_campaign.platform)||''
    //ADVANCED validation
    let node_exc_id = ''
    if(marketing_campaign.type === MARKETING_CAMPAIGN_CONSTANTS.TYPES.ADVANCED && marketing_campaign.params){
      const nodes = marketingCampaignParameters.getParamterValueFromParams(MARKETING_CAMPAIGN_CONSTANTS.PARAMETERS.NODES, marketing_campaign.params)
      const nodes_json = JSON.parse(nodes)
      const edges = marketingCampaignParameters.getParamterValueFromParams(MARKETING_CAMPAIGN_CONSTANTS.PARAMETERS.EDGES, marketing_campaign.params)
      const edges_json = JSON.parse(edges)

      nodes_json.forEach((node: MarketingNode) => {
        if(node.id=== edges_json[0].target){
          if(node.type === 'emailNode'){
            platform = MARKETING_CAMPAIGN_CONSTANTS.PLATFORMS.EMAIL
          }
          else{
            platform =  MARKETING_CAMPAIGN_CONSTANTS.PLATFORMS.SMS
          }
          marketing_campaign.params = node.data
          marketing_campaign.platform = platform
        }
      })
      node_exc_id = edges_json[0].target
    }

    const render_result = await marketingCampaignRender.renderMarketingCampaignForLead(marketing_campaign, layout, source_data)

    if(!render_result.success){
      if(enable_logs) console.log('marketing-campaign-execute-schedule.processScheduleForLead() > !render_result.success (' + marketing_campaign.id + ','+ marketing_campaign_lead.id +')', render_result)
      result.message = render_result.message
      return result
    }

    if(platform === MARKETING_CAMPAIGN_CONSTANTS.PLATFORMS.EMAIL){
      await marketingCampaignSender.sendEmail(marketing_campaign, render_result.result, marketing_campaign_lead, marketing_campaign_executed_audit_id, node_exc_id)
    }
    else if(platform === MARKETING_CAMPAIGN_CONSTANTS.PLATFORMS.SMS){
      await marketingCampaignSender.sendSMS(marketing_campaign, render_result.result, marketing_campaign_lead, from_phone, marketing_campaign_executed_audit_id, node_exc_id)
    }
    else{
      result.message = `platform '${platform}' not supported `
      return result
    }
    result.success = true

  } catch (error) {
    console.log('marketing-campaign-execute-schedule.processScheduleForLead() > error', error)
    result.message = error
  }
  return result
}

/**
 * Check and determine if the campaign should be processed
 * @param {MarketingCampaign} marketing_campaign
 * @returns {MarketingCampaignShouldBeProcessedResult} returns if it should be processed
 */
export function shouldBeProcessedMarketingCampaign(marketing_campaign: MarketingCampaign): MarketingCampaignShouldBeProcessedResult {
  const result: MarketingCampaignShouldBeProcessedResult = {
    success: false,
    message: ''
  }

  if(marketing_campaign.status !== MARKETING_CAMPAIGN_CONSTANTS.STATUSES.ACTIVE){
    result.message = `the marketingCampaign.status is ${marketing_campaign.status}`
    return result
  }

  if(!marketing_campaign.params){
    result.message = 'the marketingCampaign.params is null'
    return result
  }

  let trigger = marketingCampaignParameters.getParamterValueFromParams(MARKETING_CAMPAIGN_CONSTANTS.PARAMETERS.TRIGGER_KEY, marketing_campaign.params)
  //ADVANCED validation
  if(marketing_campaign.type === MARKETING_CAMPAIGN_CONSTANTS.TYPES.ADVANCED && marketing_campaign.params){
    trigger = MARKETING_CAMPAIGN_CONSTANTS.TRIGGERS.NONE
  }
  if(trigger !==  MARKETING_CAMPAIGN_CONSTANTS.TRIGGERS.NONE  && trigger !== MARKETING_CAMPAIGN_CONSTANTS.TRIGGERS.BIRTHDAY){
    result.message = `the marketingCampaign.params  the trigger (${trigger}) is not valid for schedule..`
    return result
  }

  const schedule = marketingCampaignParameters.getParamterValueFromParams(MARKETING_CAMPAIGN_CONSTANTS.PARAMETERS.SCHEDULE_KEY, marketing_campaign.params)
  const run_on_publish = marketingCampaignParameters.getParamterValueFromParams(MARKETING_CAMPAIGN_CONSTANTS.PARAMETERS.RUN_ON_PUBLISH, marketing_campaign.params)
  if(!schedule && !run_on_publish){
    result.message = 'the marketingCampaign.params the schedule is null'
    return result
  }

  result.success = true
  return result
}

/**
 * Check and determine if the lead should be processed
 * @param {school_business_days: SchoolBusinessDays}
 * @param {MarketingCampaign} marketing_campaign
 * @param {MarketingCampaignLead} marketing_campaign_lead
 * @returns {MarketingCampaignShouldBeProcessedResult} returns if it should be processed
 */
export function shouldBeProcessedlead(school_business_days: SchoolBusinessDays, marketing_campaign: MarketingCampaign, marketing_campaign_lead: MarketingCampaignLead): MarketingCampaignShouldBeProcessedResult{
  const result: MarketingCampaignShouldBeProcessedResult = {
    success: false,
    message: ''
  }

  const school_id = marketing_campaign.school_id
  const marketing_campaign_id = marketing_campaign.id

  if(!marketing_campaign_lead){
    result.message = `schoolId:${school_id}, marketingCampaignId:${marketing_campaign_id} > lead is null`
    return result
  }

  const expiration_days = school_business_days.getExpirationDays()
  if(expiration_days > 0
    && (marketing_campaign_lead.status === STATUSES.new || marketing_campaign_lead.status === STATUSES.archived)
    && marketing_campaign_lead.created_on !== LEAD_CREATED_ON_OPTIONS.ADMIN
    && marketing_campaign_lead.created_on !== LEAD_CREATED_ON_OPTIONS.ADMIN_RESET
    && (marketing_campaign_lead.lead_source_id && marketing_campaign_lead.lead_source_id < 0)){
    let expired = false
    if(marketing_campaign_lead.messages_count === 0){
      if(marketing_campaign_lead.created_at) {
        const created_at = new Date(moment(marketing_campaign_lead.created_at).toDate().getTime())//copy date
        if(expiration_days === 1){
          //for scholabasics extend the time on weekends & holidays
          if(school_business_days.isBusinessDay(marketing_campaign_lead.created_at)){
            //overwrite the time
            created_at.setHours(23,59,59,0)
          }
        }
        const expirationDate = school_business_days.getNextBusinessDays(created_at, expiration_days)
        const actual_date = moment.utc()
        const timer = moment.duration(expirationDate.diff(actual_date))
        expired = (timer.asHours() <= 0)
      } else {
        expired = true
      }
    }
    if(expired){
      result.message = `schoolId:${school_id}, marketingCampaignId:${marketing_campaign_id} > lead_id(${marketing_campaign_lead.id}) expired`
      return result
    }
  }

  if(marketing_campaign.audience_language === MARKETING_CAMPAIGN_CONSTANTS.AUDIENCE_LANGUAGUE.SPANISH.CODE  && marketing_campaign_lead.language !==  MARKETING_CAMPAIGN_CONSTANTS.AUDIENCE_LANGUAGUE.SPANISH.NAME){
    result.message = `schoolId:${school_id}, marketingCampaignId:${marketing_campaign_id} > lead_id(${marketing_campaign_lead.id}) the marketingCampaign.audience_language is spanish and lead is different`
    return result
  }

  if(marketing_campaign.audience_language === MARKETING_CAMPAIGN_CONSTANTS.AUDIENCE_LANGUAGUE.ENGLISH.CODE  && (marketing_campaign_lead.language !== null && marketing_campaign_lead.language !== MARKETING_CAMPAIGN_CONSTANTS.AUDIENCE_LANGUAGUE.ENGLISH.NAME)){
    result.message = `schoolId:${school_id}, marketingCampaignId:${marketing_campaign_id} > lead_id(${marketing_campaign_lead.id}) the marketingCampaign.audience_language is english and lead is different`
    return result
  }

  // COMMENTED BECAUSE THE FILTER WAS MOVED TO THE FUNCTION GetLeads
  // const lead_status_id =  Number(marketingCampaignParameters.getParamterValueFromParams(MARKETING_CAMPAIGN_CONSTANTS.PARAMETERS.LEAD_STATUS_ID_KEY, marketing_campaign.params))
  // if(lead_status_id && lead_status_id > 0){
  //   if(lead_status_id !== marketing_campaign_lead.lead_status_id){
  //     result.message = `schoolId:${school_id}, marketingCampaignId:${marketing_campaign_id} > lead.id:${marketing_campaign_lead.id} the marketingCampaign.params ${MARKETING_CAMPAIGN_CONSTANTS.PARAMETERS.LEAD_STATUS_ID_KEY} '${lead_status_id}' is different to lead.lead_status_id '${marketing_campaign_lead.lead_status_id}'`
  //     return result
  //   }
  // }
  result.success = true
  return result
}

/**
 * Inserts marketing campaign executed audit
 * @param {MarketingCampaign} markering_campaign
 * @param {string} executed
 * @param {MarketingCampaignShouldBeProcessedResult} should_be_proccessed_result
 * @returns {Promise<MarketingCampaignExecutedAudit>} returns an object marketing campaign executed audit that was inserted
 */
export async function insertExecutedAudit(markering_campaign: MarketingCampaign, executed: string, should_be_proccessed_result: MarketingCampaignShouldBeProcessedResult): Promise<MarketingCampaignExecutedAudit> {
  let result: MarketingCampaignExecutedAudit = undefined
  try {
    const insert_execute_audit: InsertMarketingCampaignExecutedAudit = {
      marketing_campaign_id: markering_campaign.id,
      school_id: markering_campaign.school_id,
      params: JSON.stringify(markering_campaign.params),
      executed: executed,
      status: should_be_proccessed_result.success === true ? 1 : 2,
      error: should_be_proccessed_result.success === false ? should_be_proccessed_result.message : '',
      created_at: moment.utc(),
    }
    result = await marketingCampaignCommon.insertMarketingCampaignExecutedAudit(insert_execute_audit)
  } catch (error) {
    console.log('marketing-campaign-execute-schedule.insertExecuteAudit() > error', error)
  }
  return result
}

/**
 * Gets the leads that meet the campaign criteria
 * @param {marketingCampaign} marketing_campaign
 * @returns {Promise<MarketingCampaignLead[]>} return an list of leads
 */
export async function getLeads(marketing_campaign: MarketingCampaign): Promise<MarketingCampaignLead[]>{
  let result: MarketingCampaignLead[] = []
  try {
    const trigger = marketingCampaignParameters.getParamterValueFromParams(MARKETING_CAMPAIGN_CONSTANTS.PARAMETERS.TRIGGER_KEY, marketing_campaign.params)
    const send_to_lead_with_status = marketingCampaignParameters.getParamterValueFromParams(MARKETING_CAMPAIGN_CONSTANTS.PARAMETERS.SENT_TO_KEY, marketing_campaign.params)

    //get lead_status_id
    let lead_status_id = marketingCampaignParameters.getParamterValueFromParams(MARKETING_CAMPAIGN_CONSTANTS.PARAMETERS.LEAD_STATUS_ID_KEY, marketing_campaign.params)

    if (lead_status_id === '-1' || lead_status_id === '-2') {
      lead_status_id = undefined //undefined is converted to null automatically by the library pg
    }

    const segmentation = marketingCampaignParameters.getParamterValueFromParams(MARKETING_CAMPAIGN_CONSTANTS.PARAMETERS.SEGMENTATION, marketing_campaign.params) || '[]'

    const segmentationValues: string[] = JSON.parse(segmentation)

    const filteringParams: { key:string, value:string }[] = segmentationValues.map((filter) => {
      const [key, value] = filter.split(':')
      return { key, value }
    })

    if (filteringParams.length === 0) {
      filteringParams.push({ key:'status', value: send_to_lead_with_status })
      filteringParams.push({ key:'lead_status_id', value: lead_status_id })
    }

    //get language
    let language = ''
    if(marketing_campaign.audience_language !== MARKETING_CAMPAIGN_CONSTANTS.AUDIENCE_LANGUAGUE.ALL.CODE){
      if(marketing_campaign.audience_language === MARKETING_CAMPAIGN_CONSTANTS.AUDIENCE_LANGUAGUE.ENGLISH.CODE){
        language = MARKETING_CAMPAIGN_CONSTANTS.AUDIENCE_LANGUAGUE.ENGLISH.NAME
      } else if(marketing_campaign.audience_language === MARKETING_CAMPAIGN_CONSTANTS.AUDIENCE_LANGUAGUE.SPANISH.CODE){
        language = MARKETING_CAMPAIGN_CONSTANTS.AUDIENCE_LANGUAGUE.SPANISH.NAME
      }
    }

    if(trigger === MARKETING_CAMPAIGN_CONSTANTS.TRIGGERS.BIRTHDAY){
      result = await marketingCampaignCommon.getLeadsBirthdaysToday(marketing_campaign.school_id, language)
    }
    else {
      result = await marketingCampaignCommon.getLeadsForSchedules(marketing_campaign.school_id, language, filteringParams)
    }
    //moved validation of DNC to queries
  } catch (error) {
    console.log('marketing-campaign-execute-schedule.getLeads() > error', error)
  }
  return result
}
