.buttonModalSummaryAI {
  background: linear-gradient(45deg, #006b8f, #0099cc);
  border: none; /* Elimina el borde por defecto para que el gradiente se vea mejor */
  position: relative;
  overflow: hidden;
  z-index: 1;

  /* Anulamos el efecto hover de antd para evitar el destello blanco */
  &:hover {
    background: linear-gradient(45deg, #006b8f, #0099cc);
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(120deg, transparent, rgba(255, 255, 255, 0.35), transparent);
    transition: all 0.7s;
    z-index: -1;
  }

  &:hover::before {
    left: 100%;
  }
}
