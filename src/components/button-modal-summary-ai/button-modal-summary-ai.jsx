import React from 'react';
import PropTypes from 'prop-types';
import { Button } from 'antd';
import styles from './button-modal-summary-ai.module.scss';

/**
 * Ícono de cerebro SVG para el botón AI Summarize.
 * @param {Object} props
 * @param {number} [props.size=24] - <PERSON><PERSON><PERSON> del ícono.
 * @param {string} [props.color='currentColor'] - Color del trazo.
 * @returns {JSX.Element}
 */
const BrainIcon = ({ size = 18, color = 'currentColor', ...props }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    stroke={color}
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    className="lucide lucide-brain-icon lucide-brain"
    {...props}>
    <path d="M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z" />
    <path d="M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z" />
    <path d="M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4" />
    <path d="M17.599 6.5a3 3 0 0 0 .399-1.375" />
    <path d="M6.003 5.125A3 3 0 0 0 6.401 6.5" />
    <path d="M3.477 10.896a4 4 0 0 1 .585-.396" />
    <path d="M19.938 10.5a4 4 0 0 1 .585.396" />
    <path d="M6 18a4 4 0 0 1-1.967-.516" />
    <path d="M19.967 17.484A4 4 0 0 1 18 18" />
  </svg>
);

BrainIcon.propTypes = {
  size: PropTypes.number,
  color: PropTypes.string,
};

/**
 * Botón estilizado para resumir con IA, incluye ícono de cerebro.
 * @component
 * @param {Object} props
 * @param {function} props.onClick - Función a ejecutar al hacer click.
 * @param {boolean} [props.disabled] - Si el botón está deshabilitado.
 * @param {'default'|'small'} [props.size] - Tamaño del botón.
 * @returns {JSX.Element}
 */
export const ButtonModalSummaryAI = ({ onClick, disabled, size = 'default' }) => {
  const isSmall = size === 'small';
  const buttonClass = `${styles.buttonModalSummaryAI} ${isSmall ? styles.small : ''}`;
  const buttonSize = isSmall ? 'small' : 'large';

  return (
    <Button
      onClick={onClick}
      disabled={disabled}
      className={buttonClass}
      size={buttonSize}
      icon={<BrainIcon size={18} color="#fff" />}>
      AI Summarize
    </Button>
  );
};

ButtonModalSummaryAI.propTypes = {
  /** Función a ejecutar al hacer click */
  onClick: PropTypes.func.isRequired,
  /** Si el botón está deshabilitado */
  disabled: PropTypes.bool,
  /** Tamaño del botón: 'default' o 'small' */
  size: PropTypes.oneOf(['default', 'small']),
};
