import { useState, useEffect } from 'react';
import { _getLeadFields } from 'controllers/leads/leads_controller';

export const useSchoolFields = (schoolId) => {
  const [schoolFields, setSchoolFields] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchLeadFields = async () => {
    // reset initial empty state if ID is null
    if (!schoolId) {
      setSchoolFields([]);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await _getLeadFields(schoolId);
      const data = await response.json();
      setSchoolFields(data || []);
    } catch (err) {
      setError(err);
      console.error('Error fetching lead fields:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchLeadFields();
  }, [schoolId]);

  return {
    schoolFields,
    loading,
    error,
  };
};
