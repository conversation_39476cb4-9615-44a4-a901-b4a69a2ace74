import React, { useState } from 'react';
import { useSelector } from 'react-redux';
import { Table, Checkbox, Flex, Button, Divider, Drawer, Tooltip, Pagination, Select, Switch } from 'antd';
import {
  PhoneOutlined,
  MessageOutlined,
  ArrowRightOutlined,
  CheckSquareOutlined,
  CheckOutlined,
  HistoryOutlined,
  FormOutlined,
  UserOutlined,
} from '@ant-design/icons';
import {
  openTaskLead,
  openSchoolDetails,
  closeSchoolDetails,
  openUpdateLead,
  setSelectedLeads,
  handleSelectAllLeads,
  clearSelectedLeads,
  setSelectedLead,
  setGlobalValue,
  clearAdvancedFilters,
} from 'redux/spot/spot-actions';
import { SchoolDetails } from '.';
import { SchoolName, RelationshipManagerHoverCard } from 'components/pages/crm-spot/shared-components';
import { useToggle } from 'hooks/useToggle';
import { _sendEmailMessageToLead, _sendSmsMessageToLead } from 'controllers/messages/messages_controllers';
import { MessageModal } from './message-modal';
import { CompareSchoolsModal } from './compare-schools-modal';
import { SubmitApplicationModal } from './submit-application-modal';
import { format, parseISO } from 'date-fns';
import { Scales16 } from '@carbon/icons-react';
import { useSchoolsMatch } from 'components/pages/crm-spot/hooks/useSchoolsMatch';
import { useCloudtalk } from 'components/pages/crm-spot/hooks/useCloudtalk';
import styles from './leads-table.module.scss';
import { useCustomColumns } from 'components/pages/crm-spot/hooks/useCustomColumns';
import { CulumnSettings } from './columns-settings';

export const LeadsTableContent = ({
  leads,
  totalLeads,
  loading,
  userId,
  schoolFields,
  handlePageChange,
  handlePageSizeChange,
  pageSize,
  currentPage,
  toggleHistoryModalOpen,
  updateAssignment,
  isSettingsOpen,
  toggleSettings,
  setSorter,
  toggleApplication,
  toggleCalling,
}) => {
  const {
    isAssignedMode,
    isSchoolDetailsOpen,
    selectedLead,
    selectedLeads,
    cloudtalkId,
    selectedSchool,
    statusFilter,
    advancedFilters,
  } = useSelector((state) => state.spot);

  const { startCall, loading: loadingCall } = useCloudtalk();

  const handleSelectAll = () => {
    if (selectedLeads.length === leads.length) {
      handleSelectAllLeads([]);
    } else {
      handleSelectAllLeads(leads);
    }
  };

  const [openMessage, toggleMessage] = useToggle(false);

  const columns = [
    {
      title: (
        <Checkbox onChange={handleSelectAll} checked={selectedLeads.length === leads.length && leads.length > 0} />
      ),
      dataIndex: 'check_id',
      key: 'check_id',
      width: 50,
      render: (check_id, lead) => (
        <Checkbox
          checked={selectedLeads.some((item) => item.id === lead.id)}
          onClick={(e) => e.stopPropagation()}
          onChange={() => setSelectedLeads(lead)}
        />
      ),
      onHeaderCell: () => ({
        style: { backgroundColor: '#00506c', color: '#ffffff' },
      }),
    },
    ...(isAssignedMode
      ? [
          {
            title: 'School',
            dataIndex: 'school_name',
            key: 'school_name',
            width: 250,
            onHeaderCell: () => ({
              style: { backgroundColor: '#00506c', color: '#ffffff' },
            }),
            render: (school_name, record) => (
              <span>
                {record.school_id} - {school_name}
                <br />
                {record.user_id_assigned ? (
                  record.user_id_assigned === userId ? (
                    <UserOutlined style={{ color: '#1890ff', fontSize: '14px' }} title="Assigned to me" />
                  ) : (
                    <>
                      <UserOutlined style={{ color: '#8c8c8c', fontSize: '12px' }} />
                      <i style={{ color: '#8c8c8c', fontSize: '12px' }}>{` ${record.email_assigned}`}</i>
                    </>
                  )
                ) : (
                  ''
                )}
              </span>
            ),
          },
        ]
      : []),
    {
      title: 'Lead ID',
      dataIndex: 'id',
      key: 'id',
      sorter: (a, b) => {
        const idA = typeof a.id === 'string' ? parseInt(a.id, 10) : a.id;
        const idB = typeof b.id === 'string' ? parseInt(b.id, 10) : b.id;
        return idA - idB;
      },
      width: 75,
      onHeaderCell: () => ({
        style: { backgroundColor: '#00506c', color: '#ffffff' },
      }),
    },
    {
      title: 'RMs',
      dataIndex: 'rms',
      key: 'rms',
      width: 75,
      onHeaderCell: () => ({
        style: { backgroundColor: '#00506c', color: '#ffffff' },
      }),
      render: (rms, record) => <RelationshipManagerHoverCard lead={record} />,
    },
    {
      title: 'Date Added',
      dataIndex: 'created_at',
      key: 'created_at',
      sorter: true,
      width: 125,
      onHeaderCell: () => ({
        style: { backgroundColor: '#00506c', color: '#ffffff' },
      }),
      render: (created_at) => {
        if (!created_at) return '';
        let dateObj;
        try {
          dateObj = typeof created_at === 'string' ? parseISO(created_at) : new Date(created_at);
        } catch {
          dateObj = new Date(created_at);
        }
        return format(dateObj, 'MM-dd-yyyy');
      },
    },
    {
      title: 'Last Updated',
      dataIndex: 'updated_at',
      key: 'updated_at',
      sorter: true,
      width: 125,
      onHeaderCell: () => ({
        style: { backgroundColor: '#00506c', color: '#ffffff' },
      }),
      render: (updated_at) => {
        if (!updated_at) return '';
        let dateObj;
        try {
          dateObj = typeof updated_at === 'string' ? parseISO(updated_at) : new Date(updated_at);
        } catch {
          dateObj = new Date(updated_at);
        }
        return format(dateObj, 'MM-dd-yyyy, HH:mm');
      },
    },
    {
      title: 'Name',
      key: 'name',
      width: 150,
      onHeaderCell: () => ({
        style: { backgroundColor: '#00506c', color: '#ffffff' },
      }),
      render: (_, record) => `${record.parent_first_name} ${record.parent_last_name}`,
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
      width: 200,
      onHeaderCell: () => ({
        style: { backgroundColor: '#00506c', color: '#ffffff' },
      }),
    },
    {
      title: 'Student',
      key: 'student',
      width: 150,
      onHeaderCell: () => ({
        style: { backgroundColor: '#00506c', color: '#ffffff' },
      }),
      render: (_, record) => `${record.child_first_name} ${record.child_last_name}`,
    },
    {
      title: 'Phone',
      dataIndex: 'phone',
      key: 'phone',
      width: 125,
      onHeaderCell: () => ({
        style: { backgroundColor: '#00506c', color: '#ffffff' },
      }),
    },
    {
      title: 'Grade',
      dataIndex: 'grade',
      key: 'grade',
      width: 75,
      onHeaderCell: () => ({
        style: { backgroundColor: '#00506c', color: '#ffffff' },
      }),
    },
    {
      title: 'Year',
      dataIndex: 'year',
      key: 'year',
      width: 75,
      onHeaderCell: () => ({
        style: { backgroundColor: '#00506c', color: '#ffffff' },
      }),
    },
    {
      title: 'Language',
      dataIndex: 'language',
      key: 'language',
      width: 100,
      onHeaderCell: () => ({
        style: { backgroundColor: '#00506c', color: '#ffffff' },
      }),
    },
    {
      title: 'Lead Source',
      dataIndex: 'lead_source',
      key: 'lead_source',
      width: 150,
      onHeaderCell: () => ({
        style: { backgroundColor: '#00506c', color: '#ffffff' },
      }),
    },
    {
      title: 'Preferred Contact',
      dataIndex: 'preferred_contact',
      key: 'preferred_contact',
      width: 150,
      onHeaderCell: () => ({
        style: { backgroundColor: '#00506c', color: '#ffffff' },
      }),
    },
    {
      title: 'Sub Stage',
      dataIndex: 'lead_status',
      key: 'lead_status',
      width: 150,
      onHeaderCell: () => ({
        style: { backgroundColor: '#00506c', color: '#ffffff' },
      }),
    },
    {
      title: 'Integration Status',
      dataIndex: 'sync_intg_status',
      key: 'sync_intg_status',
      width: 150,
      onHeaderCell: () => ({
        style: { backgroundColor: '#00506c', color: '#ffffff' },
      }),
    },
    {
      title: 'Year Accepted',
      dataIndex: 'year_accepted',
      key: 'year_accepted',
      width: 150,
      onHeaderCell: () => ({
        style: { backgroundColor: '#00506c', color: '#ffffff' },
      }),
    },
    ...(statusFilter === 'archived'
      ? [
          {
            title: 'Archived Reason',
            dataIndex: 'reason',
            key: 'reason',
            width: 150,
            onHeaderCell: () => ({
              style: { backgroundColor: '#00506c', color: '#ffffff' },
            }),
          },
        ]
      : []),
    {
      title: 'Schools Actions',
      dataIndex: 'actions',
      key: 'actions',
      fixed: 'right',
      width: 285,
      onHeaderCell: () => ({
        style: { backgroundColor: '#00506c', color: '#ffffff' },
      }),
      render: (id, record) => (
        <LeadActions
          onClick={(e) => e.stopPropagation()}
          userId={userId}
          lead={record}
          cloudtalkId={cloudtalkId}
          startCall={startCall}
          loadingCall={loadingCall}
          isAssignedMode={isAssignedMode}
          openSchoolDetails={openSchoolDetails}
          toggleMessage={toggleMessage}
          toggleHistoryModalOpen={toggleHistoryModalOpen}
          updateAssignment={updateAssignment}
          toggleApplication={toggleApplication}
          toggleCalling={toggleCalling}
        />
      ),
    },
  ];

  // parse to column format and validate data type component
  const customFields = schoolFields
    .filter((col) => col.enabled)
    .map((col) => ({
      key: col.field_indentifier,
      dataIndex: col.field_indentifier,
      title: `${col.display_name} (Custom)`,
      width: 150,
      onHeaderCell: () => ({
        style: { backgroundColor: '#00506c', color: '#ffffff' },
      }),
      render: (customValue) => {
        const isBooleanValue = col.type === 2;
        if (isBooleanValue) {
          return (
            <Switch
              id={`toggle-${col.id}`}
              checked={customValue?.toLowerCase() === 'yes'}
              checkedChildren="Yes"
              unCheckedChildren="No"
              disabled
            />
          );
        }
        return customValue;
      },
    }));

  const { columnsOrder, columnsRender, saveColumnsOrderToLocalStorage, moveColumn, toggleColumnVisibility } =
    useCustomColumns([...columns, ...customFields]);

  const saveColumns = () => {
    toggleSettings();
    // Save the current order to localStorage
    saveColumnsOrderToLocalStorage();
  };

  const classNames = {
    header: styles.drawerHeader,
    body: styles.drawerBody,
  };

  // show advanced filters applied
  const renderAdvancedFilters = () => {
    if (
      !advancedFilters ||
      Object.values(advancedFilters).every(
        (value) => value === '' || value === null || (Array.isArray(value) && value.length === 0)
      )
    )
      return null;

    const filterEntries = Object.entries(advancedFilters).filter(
      ([key, value]) =>
        value !== '' && value !== null && value !== undefined && (!Array.isArray(value) || value.length > 0)
    );

    if (filterEntries.length === 0) return null;

    const fields = {
      createdAt_from: 'From Date',
      createdAt_to: 'To Date',
      grades: 'Grades',
      search_notes: 'Notes Content',
      year: 'Year',
      language: 'Language',
      lead_source_id: 'Lead Source',
      lead_status_id: 'Lead Sub Stage',
    };

    return (
      <SelectionEdit clearSelectedLeads={clearAdvancedFilters}>
        <div style={{ lineHeight: '1.5rem' }}>
          Filtered by: <br />
          {filterEntries.map(([key, value], idx) => {
            let displayValue = Array.isArray(value) ? value.join(', ') : value;
            return (
              <span key={key}>
                {fields[key]}: <span style={{ fontWeight: 'bold' }}>{displayValue}</span>
                {idx < filterEntries.length - 1 ? ' | ' : ''}
              </span>
            );
          })}
        </div>
      </SelectionEdit>
    );
  };

  return (
    <>
      {renderAdvancedFilters()}
      {selectedLeads.length > 0 && (
        <SelectionEdit openUpdateLead={openUpdateLead} clearSelectedLeads={clearSelectedLeads}>
          {selectedLeads.length} leads selected
        </SelectionEdit>
      )}
      <Table
        className={styles.leadsTable}
        rowKey="id"
        dataSource={leads}
        columns={columnsRender}
        onChange={(pagination, filters, sorter) => {
          setSorter({
            field: sorter.field || 'created_at',
            order: sorter.order === 'ascend' ? 'asc' : sorter.order === 'descend' ? 'desc' : null,
          });
        }}
        pagination={false}
        rowClassName={(record) => (record.id === selectedLead?.id ? `${styles.row} ${styles.selectedRow}` : styles.row)}
        scroll={{ x: 1500 }}
        loading={loading}
        onRow={(lead) => ({
          onClick: () => {
            setSelectedLead(lead);
            setGlobalValue('isLeadEdition', true);
          },
        })}
      />
      {!loading && (
        <div className={styles.pagination}>
          <Select
            value={pageSize}
            onChange={handlePageSizeChange}
            style={{ width: 140 }}
            options={[
              { value: 10, label: '10 per page' },
              { value: 20, label: '20 per page' },
              { value: 50, label: '50 per page' },
              { value: 100, label: '100 per page' },
              { value: 200, label: '200 per page' },
            ]}
          />
          <Pagination
            current={currentPage}
            pageSize={pageSize}
            onChange={handlePageChange}
            total={totalLeads}
            showSizeChanger={false}
            style={{ marginLeft: 'auto' }}
          />
        </div>
      )}

      <CulumnSettings
        isSettingsOpen={isSettingsOpen}
        toggleSettings={toggleSettings}
        toggleColumnVisibility={toggleColumnVisibility}
        moveColumn={moveColumn}
        saveColumns={saveColumns}
        columnsOrder={columnsOrder}
      />

      <MessageModal open={openMessage} toggleMessage={toggleMessage} updateAssignment={updateAssignment} />

      <Drawer
        classNames={classNames}
        placement="right"
        onClose={closeSchoolDetails}
        open={isSchoolDetailsOpen}
        width={599}>
        <SchoolDetails schoolId={selectedSchool} />
      </Drawer>
    </>
  );
};

const LeadActions = ({
  userId,
  isAssignedMode,
  openSchoolDetails,
  lead,
  toggleMessage,
  cloudtalkId,
  startCall,
  loadingCall,
  toggleHistoryModalOpen,
  updateAssignment,
  toggleApplication,
  toggleCalling,
  ...props
}) => {
  const [isCompareModalOpen, setIsCompareModalOpen] = useState(false);
  const [isSubmitApplicationModalOpen, setIsSubmitApplicationModalOpen] = useState(false);
  // const [isHistoryModalOpen, setIsHistoryModalOpen] = useState(false);

  const { schools, loading, error } = useSchoolsMatch(lead.scholamatch_url) || {};

  const handleCompareSchools = () => {
    setIsCompareModalOpen(true);
  };

  const handleCloseCompareModal = () => {
    setIsCompareModalOpen(false);
  };

  const handleOpenSubmitApplicationModal = () => {
    setIsSubmitApplicationModalOpen(true);
  };

  const handleCloseSubmitApplicationModal = () => {
    setIsSubmitApplicationModalOpen(false);
  };

  const actions = [
    {
      Icon: PhoneOutlined,
      color: '#2563eb',
      onClick: () => {
        startCall({ user_id: userId, callee_number: lead.phone });
        setSelectedLead(lead);
        toggleCalling();
        setGlobalValue('isLeadEdition', true);
      },
      tooltip: `${cloudtalkId ? `${loadingCall ? 'Calling...' : 'Call'}` : 'Contact Admin to set up CloudTalk'}`,
      disabled: !cloudtalkId,
      loading: loadingCall,
    },
    {
      Icon: MessageOutlined,
      color: '#9333ea',
      onClick: () => {
        toggleMessage();
        setSelectedLead(lead);
      },
      tooltip: 'Conversations',
    },
    {
      Icon: CheckSquareOutlined,
      color: '#f97316',
      onClick: () => {
        openTaskLead();
        setSelectedLead(lead);
      },
      tooltip: 'Task',
    },
    {
      Icon: HistoryOutlined,
      color: '#2563eb',
      onClick: () => {
        setSelectedLead(lead);
        toggleHistoryModalOpen();
      },
      tooltip: 'History',
    },
    {
      Icon: !isAssignedMode ? ArrowRightOutlined : FormOutlined,
      color: '#6466f1',
      onClick: () => {
        if (!isAssignedMode) {
          window.open('https://scholamatch.com/scholaMatch-spot/basic-info', '_blank', 'noopener,noreferrer');
        } else {
          toggleApplication();
          setSelectedLead(lead);
        }
      },
      tooltip: !isAssignedMode ? 'ScholaMatch' : 'Send Application',
    },
    {
      Icon: CheckOutlined,
      color: '#6466f1',
      onClick: () => {
        openUpdateLead();
        setSelectedLead(lead);
      },
      tooltip: 'Update Stage',
    },
  ];

  return (
    <div className={styles.cellStyle} {...props}>
      {!isAssignedMode && (
        <>
          {lead.scholamatch_url ? (
            <>
              {loading ? (
                <div className="spacing-mb-8">Loading school matches...</div>
              ) : error ? (
                <div className="spacing-mb-8" style={{ color: 'red' }}>
                  Error loading schools
                </div>
              ) : schools?.length > 0 ? (
                <>
                  {schools.map((school) => (
                    <div key={school.school_id} className="spacing-mb-8">
                      <SchoolName
                        onClick={() => openSchoolDetails(school.school_id)}
                        name={school.school_name}
                        matchLevel={school.position}
                      />
                    </div>
                  ))}
                  <Button icon={<Scales16 />} className="spacing-mb-8" onClick={handleCompareSchools}>
                    Compare Schools
                  </Button>
                  <CompareSchoolsModal
                    open={isCompareModalOpen}
                    onClose={handleCloseCompareModal}
                    schools={schools || []}
                  />
                </>
              ) : (
                <div className="spacing-mb-8">No school matches found</div>
              )}
            </>
          ) : (
            <div className={styles.incomplete}>
              <p>Did Not Complete ScholaMatch</p>
            </div>
          )}
          <Divider className="spacing-my-8" />
        </>
      )}
      {/* <p className="text-dark-blue-75 text-uppercase spacing-mb-8">Quick Actions</p> */}
      <div className="">
        <Flex gap="small" justify="center" align="center">
          {actions.map((action, index) => (
            <ActionButton
              key={index}
              Icon={action.Icon}
              color={action.color}
              onClick={action.onClick}
              tooltip={action.tooltip}
              disabled={action.disabled}
              loading={action.loading}
            />
          ))}
        </Flex>
        {!isAssignedMode && schools?.length > 0 && (
          <>
            <Divider className="spacing-my-8" />
            <Button
              type="primary"
              className="spacing-mt-0 w-100"
              icon={<CheckOutlined />}
              onClick={handleOpenSubmitApplicationModal}>
              Submit Application
            </Button>
            <SubmitApplicationModal open={isSubmitApplicationModalOpen} onClose={handleCloseSubmitApplicationModal} />
          </>
        )}
      </div>
    </div>
  );
};

const ActionButton = ({ Icon, onClick, color, tooltip, disabled, loading }) => {
  return (
    <div>
      <Tooltip title={tooltip}>
        <Button
          disabled={disabled}
          type="button"
          onClick={onClick}
          className={styles.buttonStyle}
          style={{ color: color || '#000' }}
          icon={<Icon />}
          loading={loading}
        />
      </Tooltip>
    </div>
  );
};

const SelectionEdit = ({ children, openUpdateLead, clearSelectedLeads }) => {
  return (
    <div className={styles.selectionContainer}>
      <div className={styles.selectionText}>{children}</div>
      <div className={styles.selectionActions}>
        {clearSelectedLeads && (
          <Button type="default" onClick={clearSelectedLeads} className={styles.clearButton}>
            Clear Selection
          </Button>
        )}
        {openUpdateLead && (
          <Button type="primary" onClick={openUpdateLead} className={styles.updateButton}>
            Update Stage
          </Button>
        )}
      </div>
    </div>
  );
};
