.hoverCard {
  width: 20rem; // 320px
  padding: 0;
}

.header {
  background-color: #f9fafb;
  padding: 0.5rem 0.75rem;
  border-bottom: 1px solid #e5e7eb;
  font-weight: 500;
  font-size: 0.875rem;
  text-align: center;
}

.content {
  max-height: 200px;
  overflow-y: auto;
  padding-right: 0.5rem;
}

.rmItem {
  &:not(:last-child) {
    border-bottom: 1px solid #e5e7eb;
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
    padding-right: 0.5rem;
  }
}

.rmHeader {
  margin-top: 0.5rem;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.schoolName {
  font-weight: 500;
  font-size: 0.875rem;
}

.scholaId {
  color: #3b82f6;
  cursor: pointer;
  font-size: 0.75rem;
  text-decoration: underline;

  &:hover {
    color: #1d4ed8;
  }
}

.externalIcon {
  font-size: 0.625rem;
  color: #6b7280;
}

.dateEntered {
  color: #6b7280;
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

.status {
  margin-top: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
  text-align: right;

  &.applicationSent {
    color: #059669;
  }

  &.waitlisted {
    color: #d97706;
  }
}

.trigger {
  color: #3b82f6;
  font-weight: 500;
  cursor: pointer;
  text-decoration: underline;

  &:hover {
    color: #1d4ed8;
  }
}

.emptyState {
  color: #9ca3af;
}
