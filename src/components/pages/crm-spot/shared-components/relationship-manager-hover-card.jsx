import React from 'react';
import { Flex, Popover } from 'antd';
import { ExportOutlined } from '@ant-design/icons';
import { useRelatedLeads } from '../hooks/useRelatedLeads';
import { formatDate } from '../utils/format-date';
import styles from './relationship-manager-hover-card.module.scss';

export const RelationshipManagerHoverCard = ({ lead }) => {
  console.log({ lead });
  const { relatedLeads, loading } = useRelatedLeads(lead.school_id, lead.id);

  const handleScholaIdClick = (scholaId, event) => {
    event.stopPropagation();
    window.open(`/adminv2/schools/${scholaId}/relationship-manager`, '_blank');
  };

  if (loading) {
    return (
      <span>
        <span className={styles.spinner}>
          <span className="ant-spin ant-spin-spinning" style={{ fontSize: 18 }}>
            <span className="ant-spin-dot ant-spin-dot-spin">
              <i className="ant-spin-dot-item" />
              <i className="ant-spin-dot-item" />
              <i className="ant-spin-dot-item" />
              <i className="ant-spin-dot-item" />
            </span>
          </span>
        </span>
      </span>
    );
  }
  const content = (
    <div className={styles.hoverCard}>
      <div className={styles.header}>Current Relationship Managers</div>
      <div className={styles.content}>
        {relatedLeads?.map((rm, index) => (
          <div key={index} className={styles.rmItem}>
            <div className={styles.rmHeader}>
              <div>
                {(rm.child_first_name || rm.child_last_name) && (
                  <div>
                    Student:{' '}
                    <span className={styles.schoolName}>
                      {rm.child_first_name} {rm.child_last_name}
                    </span>
                  </div>
                )}
                <div>
                  School: <span className={styles.schoolName}>{rm.school}</span>
                </div>
                <div className={styles.dateEntered}>{formatDate(rm.created_at)}</div>
              </div>
              <div>
                <Flex align="center" gap="small" justify="flex-end">
                  <div className={styles.scholaId} onClick={(e) => handleScholaIdClick(rm.school_id, e)}>
                    {rm.school_id}
                  </div>
                  <ExportOutlined className={styles.externalIcon} />
                </Flex>
                <div
                  className={`${styles.status} ${
                    rm.status === 'Application Sent' ? styles.applicationSent : styles.waitlisted
                  }`}>
                  {rm.status}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  return relatedLeads?.length > 0 ? (
    <Popover content={content} trigger="hover" placement="bottomLeft" overlayStyle={{ zIndex: 1050 }}>
      <span className={styles.trigger}>{relatedLeads?.length}</span>
    </Popover>
  ) : (
    <span>0</span>
  );
};
