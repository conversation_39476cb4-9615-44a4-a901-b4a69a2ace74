import sinon from 'sinon'
import moment from 'moment'
import {http_status_codes} from '../src/error-handling/http-status-codes'
import * as applicationToLeadUtil from '../src/utils/send-application-to-lead'
import {
  marketingCampaignCommon,
  marketingCampaignCheckSchedule,
  marketingCampaignSendTest,
  facebookUtils,
  leadUtils,
  campaignUtils,
  salesforceUtils,
  applicationUtils,
  schoolUtils
} from '../src/utils'

import {marketingCampaingController as controller} from '../src/controllers'
import {MARKETING_CAMPAIGN_CONSTANTS} from '../src/constants'
import {
  CampaignReport,
  ChangeTypeMarketingCampaignRequest,
  FacebookError,
  FacebookReportByCase,
  FacebookReportByWeek,
  GetApplicationUrlForTestMarketingCampaignRequest,
  GetExecutionsByLeadIdMarketingCampaignRequest,
  GetExecutionsByLeadIdMarketingCampaignResponse,
  GetMarketingCampaignByIdRequest,
  GetMarketingCampaignCountsRequest,
  GetMarketingCampaignsRequest,
  GetMarketingCampaignsResponse,
  GetScholaMarketingCampaignsRequest,
  GetScholaMarketingCampaignsResponse,
  InsertOrUpdateMarketingCampaignRequest,
  MarketingCampaign,
  MarketingCampaignResult,
  MarketingCampaignTrackLookupData,
  RenameMarketingCampaignRequest,
  RenameMarketingCampaignResponse,
  SObjectCase,
  ScholaMarketingCampaignError,
  ScholaMarketingCampaignReport,
  School,
  SendTestMarketingCampaignRequest,
  SendTestMarketingCampaignResponse,
  SetStatusMarketingCampaignRequest,
  SetStatusMarketingCampaignResponse,
  TrackClicksRequest,
  TrackClicksResponse,
  TrackViewsRequest,
  TrackViewsResponse,
  UploadEmailCampaignSchoolLogoRequest,
  sfScholaMarketingCapaignsRes
} from '../src/interfaces'

const sandbox = sinon.createSandbox()

//mocks
jest.mock('../src/utils/send-application-to-lead')
jest.mock('../src/utils/application-utils')
jest.mock('../src/utils/marketing-campaign/marketing-campaign-common')
jest.mock('../src/utils/marketing-campaign/marketing-campaign-check-schedule')
jest.mock('../src/utils/marketing-campaign/marketing-campaign-send-test')
jest.mock('../src/utils/salesforce-utils')
jest.mock('../src/utils/campaign-utils')
jest.mock('../src/utils/facebook-utils')
jest.mock('../src/utils/school-utils')
jest.mock('../src/utils/lead-utils')


//import mocked objects
import schoolJson from './mocked-objects/school.json'

//consts
const school = schoolJson as unknown as School

const expectedError = new Error('DB Error')

const marketing_campaign: MarketingCampaign = {
  id: 1,
  school_id: 1,
  created_at: moment.utc('2022-05-10T00:29:38.205Z').toDate(),
  updated_at: moment.utc('2022-05-10T00:29:38.205Z').toDate(),
  name: 'test',
  marketing_template_id: 1,
  audience_language: 'es',
  type: 'sms',
  layout_id: 1,
  status: 'Active',
  platform: 'test',
  params: [{
    key:'1',
    value:'1',
  }],
}


const get_marketing_campaigns_response: GetMarketingCampaignsResponse = {
  results:[{
    id: 1,
    audience_language: 'es',
    layout_id: 1,
    marketing_template_id: 1,
    platform: 'test',
    name: 'test',
    status: 'Active',
    type: 'sms',
    updated_at: moment.utc('2022-05-10T00:29:38.205Z').toDate(),
    params: [{
      key:'1',
      value:'1',
    }],
  }],
  pagination:{
    page:1,
    pageCount:1,
    pageSize:1,
    rowCount:1,
  }
}

const set_status_marketing_campaign_response: SetStatusMarketingCampaignResponse = {
  success: true
}

const rename_marketing_campaign_response: RenameMarketingCampaignResponse = {
  success: true
}

const marketing_campaign_track_lookup_data: MarketingCampaignTrackLookupData = {
  ip: 'Unknown',
  country: 'Unknown',
  region: 'Unknown',
  city: 'Unknown',
  browser: 'Unknown',
  browser_version: 'Unknown',
  os: 'Unknown',
  os_version: 'Unknown',
  device: 'Unknown',
  device_version: 'Unknown',
  user_agent: 'Unknown',
}

const bufPixel = Buffer.from(MARKETING_CAMPAIGN_CONSTANTS.TRACKING_PIXEL.STRING_BASE_64, 'base64')
const track_views_response: TrackViewsResponse = {
  image: bufPixel,
  content_type: MARKETING_CAMPAIGN_CONSTANTS.TRACKING_PIXEL.CONTENT_TYPE
}

const track_clicks_response: TrackClicksResponse = {
  url_to_redirect_to: 'http://abc.test.com'
}


const marketing_campaign_result_string_success: MarketingCampaignResult<string[]> = {
  success: true,
  message: '',
  result: ['a','b','c']

}

const marketing_campaign_result_string_unsuccess: MarketingCampaignResult<string[]> = {
  success: false,
  message: '',
  result: []
}

const send_test_marketing_campaign_response_success: SendTestMarketingCampaignResponse = {
  success: true,
  message: 'abc'
}

const send_test_marketing_campaign_response_unsuccess: SendTestMarketingCampaignResponse = {
  success: false,
  message: 'abc'
}

const s_object_case_default_01: SObjectCase = {
  Id: '1',
  id: '1',
  Status: 'test',
  Campaign_Start_Date__c: '2023-01-01',
  Campaign_End_Date__c: '2023-02-01',
  Boost_Amount__c: '10',
  Campaign_Budget__c: '10',
  Campaign_Approval_Date__c: '2023-01-01',
  LastModifiedDate: '2023-01-01',
  Public_Description__c: 'test',
  Subject: 'test',
  Account: 'test',
  expr0: 1,
  CreatedDate: '2023-01-01'
}

const campaign_report_default: CampaignReport = {
  id: 1,
  school_id: 1,
  total_added_revenue: 1,
  total_leads_received: 1,
  cost_per_lead: 1,
  cost_per_lead_campaign: 1,
  cost_per_applicant: 1,
  start_date: moment.utc('2023-01-01').toDate(),
  end_date: moment.utc('2023-02-01').toDate(),
  type: 'test',
  status: 'status',
  profile_views: 1,
  reachs: 1,
  impressions: 1,
  campaig_start_date: moment.utc('2023-01-01').toDate(),
  campaig_end_date: moment.utc('2023-02-01').toDate(),
  created_at: moment.utc('2023-01-01').toDate(),
  updated_at: moment.utc('2023-01-01').toDate(),
  campaign_spending_limit: 1,
  application_received_manual: 1,
  weekly_analysis: 'test',
  campaign_budget_remaining: 1,
  case_id: 'test',
  deleted: false,
}

const facebook_error_default: FacebookError = {
  error: 'error test'
}

const facebook_report_by_week_Lifetime_default: FacebookReportByWeek = {
  id: 2,
  start_date: '2023/02/01',
  end_date: '2023/02/02',
  school_id: 1,
  total_added_revenue: 2,
  total_leads_received: 2,
  cost_per_lead_campaign: 2,
  cost_per_applicant: 2,
  campaig_start_date: '2023/02/01',
  campaig_end_date: '2023/02/02',
  campaign_spending_limit: 2,
  type: 'test2',
  status: 'test2',
  profile_views: 2,
  reachs: 2,
  impressions: 2,
  case_id: 'test',

  //Lifetime
  isLifetime: true,
  campaign_budget_remaining: 2,
  cpl_cv: 2,
  cost_per_lead: 2,
  cpl_inter: 2,
  daily_budget:2,
  updated_at: moment.utc('2023/02/01').toDate(),
  description: 'test2',
  historical_margin: 2,

  //Campaign Reports
  total_spend_week: 2,
  cpl: 2,
  margin: 2,
  total_spend: 2,
  general_leads: 2,
  total_spend_general_leads: 2
}


const facebook_report_by_case_default: FacebookReportByCase = {
  campaign_id: 'campaig_1',
  reportByWeeks: [...[facebook_report_by_week_Lifetime_default]]
}

describe('Marketing Campaign controller', () => {

  afterEach( () => {
    sandbox.restore()
  })
  beforeEach( () => {
    jest.resetModules()
    jest.resetAllMocks()
    jest.restoreAllMocks()
  })

  describe('getScholaMarketingCampaigns', () => {
    const params: GetScholaMarketingCampaignsRequest = {
      school_id: 1,
      page: 1,
      pageSize: 10
    }
    test('is a function', () => {
      expect(typeof controller.getScholaMarketingCampaigns).toBe('function')
    })
    test('throw error code 400 with message "Missing Parameters" if no object is provided', async() => {
      try {
        await controller.getScholaMarketingCampaigns(undefined)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Missing Parameters')
      }
    })
    test('throw error code 400 with message "Missing Parameters" if the object is empty', async() => {
      try {
        await controller.getScholaMarketingCampaigns({} as GetScholaMarketingCampaignsRequest)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Missing Parameters')
      }
    })
    test('throw error code 400 with message "Missing Parameters" if the object is missing required properties', async() => {
      try {
        await controller.getScholaMarketingCampaigns({page: 1} as GetScholaMarketingCampaignsRequest)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Missing Parameters')
      }
    })
    test('throw error code 400 with message "Bad request" if the object has additional properties', async() => {
      try {
        await controller.getScholaMarketingCampaigns({...params, test: true} as GetScholaMarketingCampaignsRequest)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Bad Request')
      }
    })
    test('when there is error on salesforceUtils', async() => {
      jest.spyOn(salesforceUtils, 'getScholaMarketingCampaigns').mockImplementationOnce(()=> { throw expectedError })
      try {
        const result = await controller.getScholaMarketingCampaigns(params) as unknown as ScholaMarketingCampaignError
        expect(result).not.toBeNull()
        expect(result).not.toBeUndefined()
        expect(result.error).not.toBeUndefined()
      } catch (e) {
        expect(true).toBe(false)
      }
    })
    test('when salesforce return empty cases', async() => {
      const sf_schola_marketing_capaigns_res: sfScholaMarketingCapaignsRes = {
        cases: [],
        rowCount: 0,
        FB_CampaignId__c: 'FB_CampaignId__c',
        FB_AccountId__c: 'FB_AccountId__c'
      }
      jest.spyOn(salesforceUtils, 'getScholaMarketingCampaigns').mockImplementationOnce(()=> Promise.resolve(sf_schola_marketing_capaigns_res))
      try {
        const result = await controller.getScholaMarketingCampaigns(params) as GetScholaMarketingCampaignsResponse
        expect(result).not.toBeNull()
        expect(result).not.toBeUndefined()
        expect(result.results).not.toBeUndefined()
        expect(result.results).toEqual([])
        expect(result.pagination).not.toBeUndefined()
        expect(result.pagination.page).toEqual(1)
        expect(result.pagination.pageCount).toEqual(0)
        expect(result.pagination.pageSize).toEqual(params.pageSize)
      } catch (e) {
        expect(true).toBe(false)
      }
    })
    test('when salesforce return a case with Campaign_Start_Date__c is undefined', async() => {
      const case_1 = {...s_object_case_default_01}
      case_1.Campaign_Start_Date__c = undefined
      const sf_schola_marketing_capaigns_res: sfScholaMarketingCapaignsRes = {
        cases: [case_1],
        rowCount: 1,
        FB_CampaignId__c: 'FB_CampaignId__c',
        FB_AccountId__c: 'FB_AccountId__c'
      }
      jest.spyOn(salesforceUtils, 'getScholaMarketingCampaigns').mockImplementationOnce(()=> Promise.resolve(sf_schola_marketing_capaigns_res))
      try {
        const result = await controller.getScholaMarketingCampaigns(params) as GetScholaMarketingCampaignsResponse

        expect(result).not.toBeNull()
        expect(result).not.toBeUndefined()
        expect(result.results).not.toBeUndefined()
        expect(result.results.length).toEqual(1)
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        expect((result.results[0] as any).Warn).not.toBeUndefined()
        expect(result.pagination).not.toBeUndefined()
        expect(result.pagination.page).toEqual(1)
        expect(result.pagination.pageCount).toEqual(1)
        expect(result.pagination.pageSize).toEqual(params.pageSize)
      } catch (e) {
        expect(true).toBe(false)
      }
    })
    test('when salesforce return cases with values on Campaign_Start_Date__c and Campaign_End_Date__c and there is campaign_report', async() => {
      //call salesforceUtils.getScholaMarketingCampaigns
      const case_1 = {...s_object_case_default_01}
      case_1.Campaign_Start_Date__c = '2023-01-01'
      case_1.Campaign_End_Date__c = '2023-02-01'
      const sf_schola_marketing_capaigns_res: sfScholaMarketingCapaignsRes = {
        cases: [case_1],
        rowCount: 1,
        FB_CampaignId__c: 'FB_CampaignId__c',
        FB_AccountId__c: 'FB_AccountId__c'
      }
      jest.spyOn(salesforceUtils, 'getScholaMarketingCampaigns').mockImplementationOnce(()=> Promise.resolve(sf_schola_marketing_capaigns_res))

      //call campaignUtils.getCampaignReportsBySchoolIdAndCaseId
      jest.spyOn(campaignUtils, 'getCampaignReportsBySchoolIdAndCaseId').mockImplementationOnce(()=> Promise.resolve([campaign_report_default]))

      //applicationUtils.getApplicationsReceivedCount
      jest.spyOn(applicationUtils, 'getApplicationsReceivedCount').mockImplementationOnce(()=> Promise.resolve(10))

      //call schoolUtils.getAmountFromStatesFundings
      jest.spyOn(schoolUtils, 'getAmountFromStatesFundings').mockImplementationOnce(()=> Promise.resolve(100))
      try {
        const result = await controller.getScholaMarketingCampaigns(params) as GetScholaMarketingCampaignsResponse

        expect(result).not.toBeNull()
        expect(result).not.toBeUndefined()
        expect(result.results).not.toBeUndefined()
        expect(result.results.length).toEqual(1)

        const result_results_1 = result.results[0] as unknown as  ScholaMarketingCampaignReport
        expect(result_results_1).not.toBeUndefined()
        expect(result_results_1.name).toEqual('test')
        expect(result_results_1.leads).toEqual(1)
        expect(result_results_1.applications).toEqual('10')
        expect(result_results_1.potential_revenue).toEqual(100)

        expect(result.pagination).not.toBeUndefined()
        expect(result.pagination.page).toEqual(1)
        expect(result.pagination.pageCount).toEqual(1)
        expect(result.pagination.pageSize).toEqual(params.pageSize)
      } catch (e) {
        expect(true).toBe(false)
      }
    })
    test('when salesforce return cases and fecebook return error', async() => {
      //call salesforceUtils.getScholaMarketingCampaigns
      const case_1 = {...s_object_case_default_01}
      case_1.Campaign_Start_Date__c = '2023-01-01'
      case_1.Campaign_End_Date__c = '2023-02-01'
      const sf_schola_marketing_capaigns_res: sfScholaMarketingCapaignsRes = {
        cases: [case_1],
        rowCount: 1,
        FB_CampaignId__c: 'FB_CampaignId__c',
        FB_AccountId__c: 'FB_AccountId__c'
      }
      jest.spyOn(salesforceUtils, 'getScholaMarketingCampaigns').mockImplementationOnce(()=> Promise.resolve(sf_schola_marketing_capaigns_res))

      //call campaignUtils.getCampaignReportsBySchoolIdAndCaseId
      jest.spyOn(campaignUtils, 'getCampaignReportsBySchoolIdAndCaseId').mockImplementationOnce(()=> Promise.resolve([]))

      //call getFBData
      jest.spyOn(facebookUtils, 'getReportByCase').mockImplementationOnce(()=> Promise.resolve(facebook_report_by_case_default))
      jest.spyOn(facebookUtils, 'getEnrichReport').mockImplementationOnce(()=> Promise.resolve(undefined))
      jest.spyOn(facebookUtils, 'getEnrichLifetime').mockImplementationOnce(()=> Promise.resolve(facebook_error_default))

      //applicationUtils.getApplicationsReceivedCount
      jest.spyOn(applicationUtils, 'getApplicationsReceivedCount').mockImplementationOnce(()=> Promise.resolve(10))

      //call schoolUtils.getAmountFromStatesFundings
      jest.spyOn(schoolUtils, 'getAmountFromStatesFundings').mockImplementationOnce(()=> Promise.resolve(100))
      try {
        const result = await controller.getScholaMarketingCampaigns(params) as GetScholaMarketingCampaignsResponse

        expect(result).not.toBeNull()
        expect(result).not.toBeUndefined()
        expect(result.results).not.toBeUndefined()
        expect(result.results.length).toEqual(0)
        expect(result.pagination).not.toBeUndefined()
        expect(result.pagination.page).toEqual(1)
        expect(result.pagination.pageCount).toEqual(1)
        expect(result.pagination.pageSize).toEqual(params.pageSize)
      } catch (e) {
        expect(true).toBe(false)
      }
    })
    test('when salesforce return cases and fecebook return facebook_report_by_week_Lifetime', async() => {
      //call salesforceUtils.getScholaMarketingCampaigns
      const case_1 = {...s_object_case_default_01}
      case_1.Campaign_Start_Date__c = '2023-01-01'
      case_1.Campaign_End_Date__c = '2023-02-01'
      const sf_schola_marketing_capaigns_res: sfScholaMarketingCapaignsRes = {
        cases: [case_1],
        rowCount: 1,
        FB_CampaignId__c: 'FB_CampaignId__c',
        FB_AccountId__c: 'FB_AccountId__c'
      }
      jest.spyOn(salesforceUtils, 'getScholaMarketingCampaigns').mockImplementationOnce(()=> Promise.resolve(sf_schola_marketing_capaigns_res))

      //call campaignUtils.getCampaignReportsBySchoolIdAndCaseId
      jest.spyOn(campaignUtils, 'getCampaignReportsBySchoolIdAndCaseId').mockImplementationOnce(()=> Promise.resolve([]))

      //call getFBData
      jest.spyOn(facebookUtils, 'getReportByCase').mockImplementationOnce(()=> Promise.resolve(facebook_report_by_case_default))
      jest.spyOn(facebookUtils, 'getEnrichReport').mockImplementationOnce(()=> Promise.resolve(undefined))
      jest.spyOn(facebookUtils, 'getEnrichLifetime').mockImplementationOnce(()=> Promise.resolve([facebook_report_by_week_Lifetime_default]))

      //applicationUtils.getApplicationsReceivedCount
      jest.spyOn(applicationUtils, 'getApplicationsReceivedCount').mockImplementationOnce(()=> Promise.resolve(50))

      //call schoolUtils.getAmountFromStatesFundings
      jest.spyOn(schoolUtils, 'getAmountFromStatesFundings').mockImplementationOnce(()=> Promise.resolve(200))
      try {
        const result = await controller.getScholaMarketingCampaigns(params) as GetScholaMarketingCampaignsResponse
        expect(result).not.toBeNull()
        expect(result).not.toBeUndefined()
        expect(result.results).not.toBeUndefined()
        expect(result.results.length).toEqual(1)

        const result_results_1 = result.results[0] as unknown as  ScholaMarketingCampaignReport
        expect(result_results_1).not.toBeUndefined()
        expect(result_results_1.name).toEqual('test')
        expect(result_results_1.leads).toEqual(2)
        expect(result_results_1.applications).toEqual('50')
        expect(result_results_1.potential_revenue).toEqual(400)

        expect(result.pagination).not.toBeUndefined()
        expect(result.pagination.page).toEqual(1)
        expect(result.pagination.pageCount).toEqual(1)
        expect(result.pagination.pageSize).toEqual(params.pageSize)
      } catch (e) {
        expect(true).toBe(false)
      }
    })
  })

  describe('getMarketingCampaigns', () => {
    const params: GetMarketingCampaignsRequest = {
      school_id: 1,
      page: 1,
      pageSize: 1,
      platform: 'email',
      archived:  false,
    }

    test('is a function', () => {
      expect(typeof controller.getMarketingCampaigns).toBe('function')
    })
    test('throw error code 400 with message "Missing Parameters" if no object is provided', async() => {
      try {
        await controller.getMarketingCampaigns(undefined)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Missing Parameters')
      }
    })
    test('throw error code 400 with message "Missing Parameters" if the object is empty', async() => {
      try {
        await controller.getMarketingCampaigns({} as GetMarketingCampaignsRequest)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Missing Parameters')
      }
    })
    test('throw error code 400 with message "Missing Parameters" if the object is missing required properties', async() => {
      try {
        await controller.getMarketingCampaigns({page: 1} as GetMarketingCampaignsRequest)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Missing Parameters')
      }
    })
    test('throw error code 400 with message "Bad request" if the object has additional properties', async() => {
      try {
        await controller.getMarketingCampaigns({...params, test: true} as GetMarketingCampaignsRequest)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Bad Request')
      }
    })
    test('throw error code 500 if something is wrong with the database connection', async() => {
      jest.spyOn(marketingCampaignCommon, 'getMarketingCampaigns').mockImplementationOnce(()=> { throw expectedError })
      try {
        await controller.getMarketingCampaigns(params)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.INTERNAL_SERVER)
      }
    })
    test('returns an undefined object in the result', async() => {
      jest.spyOn(marketingCampaignCommon, 'getMarketingCampaigns').mockResolvedValueOnce(Promise.resolve(undefined))
      const response = await controller.getMarketingCampaigns(params)
      expect(response).toBe(undefined)
    })
    test('returns an valid object in the result', async() => {
      jest.spyOn(marketingCampaignCommon, 'getMarketingCampaigns').mockResolvedValueOnce(Promise.resolve(get_marketing_campaigns_response))
      const response = await controller.getMarketingCampaigns(params)
      expect(response).toBe(get_marketing_campaigns_response)
    })
  })
  describe('getMarketingCampaignById', () => {
    const params: GetMarketingCampaignByIdRequest = {
      school_id: 1,
      campaign_id: 1,
    }

    test('is a function', () => {
      expect(typeof controller.getMarketingCampaignById).toBe('function')
    })

    test('throw error code 400 with message "Missing Parameters" if no object is provided', async() => {
      try {
        await controller.getMarketingCampaignById(undefined)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Missing Parameters')
      }
    })
    test('throw error code 400 with message "Missing Parameters" if the object is empty', async() => {
      try {
        await controller.getMarketingCampaignById({} as GetMarketingCampaignByIdRequest)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Missing Parameters')
      }
    })
    test('throw error code 400 with message "Missing Parameters" if the object is missing required properties', async() => {
      try {
        await controller.getMarketingCampaignById({school_id: 1} as GetMarketingCampaignByIdRequest)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Missing Parameters')
      }
    })
    test('throw error code 400 with message "Bad request" if the object has additional properties', async() => {
      try {
        await controller.getMarketingCampaignById({...params, test: true} as GetMarketingCampaignByIdRequest)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Bad Request')
      }
    })
    test('throw error code 500 if something is wrong with the database connection', async() => {
      jest.spyOn(marketingCampaignCommon, 'getMarketingCampaignById').mockImplementationOnce(()=> { throw expectedError })
      try {
        await controller.getMarketingCampaignById(params)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.INTERNAL_SERVER)
      }
    })
    test('returns an undefined object in the result', async() => {
      jest.spyOn(marketingCampaignCommon, 'getMarketingCampaignById').mockResolvedValueOnce(Promise.resolve(undefined))
      const response = await controller.getMarketingCampaignById(params)
      expect(response).toBe(undefined)
    })
    test('returns an valid object in the result', async() => {
      jest.spyOn(marketingCampaignCommon, 'getMarketingCampaignById').mockResolvedValueOnce(Promise.resolve(marketing_campaign))
      const response = await controller.getMarketingCampaignById(params)
      expect(response).toBe(marketing_campaign)
    })
  })
  describe('insertOrUpdateMarketingCampaign', () => {
    const params: InsertOrUpdateMarketingCampaignRequest = {
      id:1,
      school_id: 1,
      platform: 'email',
      type: 'Recurring Automated Email',
      name: 'test'  ,
      audience_language: 'en',
      layout_id: 1,
      marketing_template_id:1,
      params:[{
        key:'key1',
        value:'value2'
      }]
    }

    test('is a function', () => {
      expect(typeof controller.insertOrUpdateMarketingCampaign).toBe('function')
    })

    test('throw error code 400 with message "Missing Parameters" if no object is provided', async() => {
      try {
        await controller.insertOrUpdateMarketingCampaign(undefined, 'test')
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Missing Parameters')
      }
    })
    test('throw error code 400 with message "Missing Parameters" if the object is empty', async() => {
      try {
        await controller.insertOrUpdateMarketingCampaign({} as InsertOrUpdateMarketingCampaignRequest, 'test')
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Missing Parameters')
      }
    })
    test('throw error code 400 with message "Missing Parameters" if the object is missing required properties', async() => {
      try {
        await controller.insertOrUpdateMarketingCampaign({school_id: 1} as InsertOrUpdateMarketingCampaignRequest,'test')
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Missing Parameters')
      }
    })
    test('throw error code 400 with message "Bad request" if the object has additional properties', async() => {
      try {
        await controller.insertOrUpdateMarketingCampaign({...params, test: true} as InsertOrUpdateMarketingCampaignRequest,'test')
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Bad Request')
      }
    })
    test('throw error code 500 if something is wrong with the database connection', async() => {
      jest.spyOn(marketingCampaignCommon, 'getMarketingCampaignById').mockImplementationOnce(()=> { throw expectedError })
      try {
        await controller.insertOrUpdateMarketingCampaign(params, 'test')
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.INTERNAL_SERVER)
      }
    })
    test('returns an undefined object in the result', async() => {
      jest.spyOn(marketingCampaignCommon, 'getMarketingCampaignById').mockResolvedValueOnce(Promise.resolve(undefined))
      const response = await controller.insertOrUpdateMarketingCampaign(params,'test')
      expect(response).toBe(undefined)
    })
    test('returns an valid object in the result when is insert', async() => {
      jest.spyOn(marketingCampaignCommon, 'getMarketingCampaignById').mockResolvedValueOnce(Promise.resolve(undefined))
      jest.spyOn(marketingCampaignCommon, 'insertMarketingCampaign').mockResolvedValueOnce(Promise.resolve(marketing_campaign))
      const response = await controller.insertOrUpdateMarketingCampaign(params, 'test')
      expect(response).toBe(marketing_campaign)
    })
    test('returns an valid object in the result when is update', async() => {
      jest.spyOn(marketingCampaignCommon, 'getMarketingCampaignById').mockResolvedValueOnce(Promise.resolve(marketing_campaign))
      jest.spyOn(marketingCampaignCommon, 'updateMarketingCampaign').mockResolvedValueOnce(Promise.resolve(marketing_campaign))
      const response = await controller.insertOrUpdateMarketingCampaign(params, 'test')
      expect(response).toBe(marketing_campaign)
    })
  })
  describe('setStatusMarketingCampaign', () => {
    const params: SetStatusMarketingCampaignRequest = {
      school_id: 1,
      campaign_id: 1,
      status: 'Active'
    }

    test('is a function', () => {
      expect(typeof controller.setStatusMarketingCampaign).toBe('function')
    })

    test('throw error code 400 with message "Missing Parameters" if no object is provided', async() => {
      try {
        await controller.setStatusMarketingCampaign(undefined,'')
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Missing Parameters')
      }
    })
    test('throw error code 400 with message "Missing Parameters" if the object is empty', async() => {
      try {
        await controller.setStatusMarketingCampaign({} as SetStatusMarketingCampaignRequest, '')
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Missing Parameters')
      }
    })
    test('throw error code 400 with message "Missing Parameters" if the object is missing required properties', async() => {
      try {
        await controller.setStatusMarketingCampaign({school_id: 1} as SetStatusMarketingCampaignRequest, '')
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Missing Parameters')
      }
    })
    test('throw error code 400 with message "Bad request" if the object has additional properties', async() => {
      try {
        await controller.setStatusMarketingCampaign({...params, test: true} as SetStatusMarketingCampaignRequest, '')
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Bad Request')
      }
    })
    test('throw error code 500 if something is wrong with the database connection', async() => {
      jest.spyOn(marketingCampaignCommon, 'setStatusMarketingCampaign').mockImplementationOnce(()=> { throw expectedError })
      try {
        await controller.setStatusMarketingCampaign(params, '')
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.INTERNAL_SERVER)
      }
    })
    test('returns an valid object in the result', async() => {
      jest.spyOn(marketingCampaignCommon, 'setStatusMarketingCampaign').mockResolvedValueOnce(undefined)
      const response = await controller.setStatusMarketingCampaign(params, '')
      expect(response).toStrictEqual(set_status_marketing_campaign_response)
    })
  })
  describe('renameMarketingCampaign', () => {
    const params: RenameMarketingCampaignRequest = {
      school_id: 1,
      campaign_id: 1,
      name: 'new Name'
    }

    test('is a function', () => {
      expect(typeof controller.renameMarketingCampaign).toBe('function')
    })

    test('throw error code 400 with message "Missing Parameters" if no object is provided', async() => {
      try {
        await controller.renameMarketingCampaign(undefined)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Missing Parameters')
      }
    })
    test('throw error code 400 with message "Missing Parameters" if the object is empty', async() => {
      try {
        await controller.renameMarketingCampaign({} as RenameMarketingCampaignRequest)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Missing Parameters')
      }
    })
    test('throw error code 400 with message "Missing Parameters" if the object is missing required properties', async() => {
      try {
        await controller.renameMarketingCampaign({school_id: 1} as RenameMarketingCampaignRequest)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Missing Parameters')
      }
    })
    test('throw error code 400 with message "Bad request" if the object has additional properties', async() => {
      try {
        await controller.renameMarketingCampaign({...params, test: true} as RenameMarketingCampaignRequest)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Bad Request')
      }
    })
    test('throw error code 500 if something is wrong with the database connection', async() => {
      jest.spyOn(marketingCampaignCommon, 'renameMarketingCampaign').mockImplementationOnce(()=> { throw expectedError })
      try {
        await controller.renameMarketingCampaign(params)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.INTERNAL_SERVER)
      }
    })
    test('returns an valid object in the result', async() => {
      jest.spyOn(marketingCampaignCommon, 'renameMarketingCampaign').mockResolvedValueOnce(undefined)
      const response = await controller.renameMarketingCampaign(params)
      expect(response).toStrictEqual(rename_marketing_campaign_response)
    })
  })
  describe('changeTypeMarketingCampaign', () => {
    const params: ChangeTypeMarketingCampaignRequest = {
      school_id: 1,
      campaign_id: 1,
      type: 'Regular Mass Email'
    }

    test('is a function', () => {
      expect(typeof controller.changeTypeMarketingCampaign).toBe('function')
    })

    test('throw error code 400 with message "Missing Parameters" if no object is provided', async() => {
      try {
        await controller.changeTypeMarketingCampaign(undefined)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Missing Parameters')
      }
    })
    test('throw error code 400 with message "Missing Parameters" if the object is empty', async() => {
      try {
        await controller.changeTypeMarketingCampaign({} as ChangeTypeMarketingCampaignRequest)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Missing Parameters')
      }
    })
    test('throw error code 400 with message "Missing Parameters" if the object is missing required properties', async() => {
      try {
        await controller.changeTypeMarketingCampaign({school_id: 1} as ChangeTypeMarketingCampaignRequest)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Missing Parameters')
      }
    })
    test('throw error code 400 with message "Bad request" if the object has additional properties', async() => {
      try {
        await controller.changeTypeMarketingCampaign({...params, test: true} as ChangeTypeMarketingCampaignRequest)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Bad Request')
      }
    })
    test('throw error code 500 if something is wrong with the database connection', async() => {
      jest.spyOn(marketingCampaignCommon, 'changeTypeMarketingCampaign').mockImplementationOnce(()=> { throw expectedError })
      try {
        await controller.changeTypeMarketingCampaign(params)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.INTERNAL_SERVER)
      }
    })
    test('returns an valid object in the result', async() => {
      jest.spyOn(marketingCampaignCommon, 'changeTypeMarketingCampaign').mockResolvedValueOnce(undefined)
      const response = await controller.changeTypeMarketingCampaign(params)
      expect(response).toStrictEqual(rename_marketing_campaign_response)
    })
  })
  describe('trackViews', () => {
    const params: TrackViewsRequest = {
      campaign_id: 1,
      identifier:'123'
    }

    test('is a function', () => {
      expect(typeof controller.trackViews).toBe('function')
    })

    test('throw error code 400 with message "Missing Parameters" if no object is provided', async() => {
      try {
        await controller.trackViews(undefined)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Missing Parameters')
      }
    })
    test('throw error code 400 with message "Missing Parameters" if the object is empty', async() => {
      try {
        await controller.trackViews({} as TrackViewsRequest)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Missing Parameters')
      }
    })
    test('throw error code 400 with message "Missing Parameters" if the object is missing required properties', async() => {
      try {
        await controller.trackViews({campaign_id: 1} as TrackViewsRequest)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Missing Parameters')
      }
    })
    test('throw error code 400 with message "Bad request" if the object has additional properties', async() => {
      try {
        await controller.trackViews({...params, test: true} as TrackViewsRequest)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Bad Request')
      }
    })
    test('throw error code 500 if something is wrong with the database connection', async() => {
      jest.spyOn(marketingCampaignCommon, 'getLookupData').mockImplementationOnce(()=> { throw expectedError })
      try {
        await controller.trackViews(params)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.INTERNAL_SERVER)
      }
    })
    test('returns an valid object in the result', async() => {
      jest.spyOn(marketingCampaignCommon, 'getLookupData').mockImplementationOnce(()=> marketing_campaign_track_lookup_data)
      jest.spyOn(marketingCampaignCommon, 'insertOrUpdateMarketingCampaignTracker').mockResolvedValueOnce(undefined)
      const response = await controller.trackViews(params)
      expect(response).toStrictEqual(track_views_response)
    })
  })
  describe('trackClicks', () => {
    const params: TrackClicksRequest = {
      campaign_id: 1,
      identifier:'123',
      location: 'abc.test.com',
      url_type: 'custom'
    }

    test('is a function', () => {
      expect(typeof controller.trackClicks).toBe('function')
    })

    test('throw error code 400 with message "Missing Parameters" if no object is provided', async() => {
      try {
        await controller.trackClicks(undefined)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Missing Parameters')
      }
    })
    test('throw error code 400 with message "Missing Parameters" if the object is empty', async() => {
      try {
        await controller.trackClicks({} as TrackClicksRequest)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Missing Parameters')
      }
    })
    test('throw error code 400 with message "Missing Parameters" if the object is missing required properties', async() => {
      try {
        await controller.trackClicks({campaign_id: 1} as TrackClicksRequest)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Missing Parameters')
      }
    })
    test('throw error code 400 with message "Bad request" if the object has additional properties', async() => {
      try {
        await controller.trackClicks({...params, test: true} as TrackClicksRequest)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Bad Request')
      }
    })
    test('throw error code 500 if something is wrong with the database connection', async() => {
      jest.spyOn(marketingCampaignCommon, 'getLookupData').mockImplementationOnce(()=> { throw expectedError })
      try {
        await controller.trackClicks(params)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.INTERNAL_SERVER)
      }
    })
    test('returns an valid object in the result', async() => {
      jest.spyOn(marketingCampaignCommon, 'getLookupData').mockImplementationOnce(()=> marketing_campaign_track_lookup_data)
      jest.spyOn(marketingCampaignCommon, 'insertOrUpdateMarketingCampaignTracker').mockResolvedValueOnce(undefined)
      const response = await controller.trackClicks(params)
      expect(response).toStrictEqual(track_clicks_response)
    })
    test('returns an valid object in the result when the location start http://', async() => {
      const params_override = {...params}
      params_override.location = 'http://www.test.test.com'
      jest.spyOn(marketingCampaignCommon, 'getLookupData').mockImplementationOnce(()=> marketing_campaign_track_lookup_data)
      jest.spyOn(marketingCampaignCommon, 'insertOrUpdateMarketingCampaignTracker').mockResolvedValueOnce(undefined)
      const response = await controller.trackClicks(params_override)
      expect(response).not.toBeUndefined()
      expect(response.url_to_redirect_to).toEqual(params_override.location)
    })
    test('returns an valid object in the result when the location start https:// ', async() => {
      const params_override = {...params}
      params_override.location = 'https://www.test.test.com'
      jest.spyOn(marketingCampaignCommon, 'getLookupData').mockImplementationOnce(()=> marketing_campaign_track_lookup_data)
      jest.spyOn(marketingCampaignCommon, 'insertOrUpdateMarketingCampaignTracker').mockResolvedValueOnce(undefined)
      const response = await controller.trackClicks(params_override)
      expect(response).not.toBeUndefined()
      expect(response.url_to_redirect_to).toEqual(params_override.location)
    })
    test('returns an valid object in the result when the location does not start http:// or https://', async() => {
      const params_override = {...params}
      params_override.location = 'www.test.test.com'
      jest.spyOn(marketingCampaignCommon, 'getLookupData').mockImplementationOnce(()=> marketing_campaign_track_lookup_data)
      jest.spyOn(marketingCampaignCommon, 'insertOrUpdateMarketingCampaignTracker').mockResolvedValueOnce(undefined)
      const response = await controller.trackClicks(params_override)
      expect(response).not.toBeUndefined()
      expect(response.url_to_redirect_to).toEqual(`http://${params_override.location}`)
    })
  })
  describe('getMarketingCampaignCounts', () => {
    const params: GetMarketingCampaignCountsRequest = {
      school_id: 1,
      ids:'campaignEmail,campaignSMS',
      filtering: "['campaignSMS', 'campaignSMS']"
    }

    test('is a function', () => {
      expect(typeof controller.getMarketingCampaignCounts).toBe('function')
    })

    test('throw error code 400 with message "Missing Parameters" if no object is provided', async() => {
      try {
        await controller.getMarketingCampaignCounts(undefined)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Missing Parameters')
      }
    })
    test('throw error code 400 with message "Missing Parameters" if the object is empty', async() => {
      try {
        await controller.getMarketingCampaignCounts({} as GetMarketingCampaignCountsRequest)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Missing Parameters')
      }
    })
    test('throw error code 400 with message "Missing Parameters" if the object is missing required properties', async() => {
      try {
        await controller.getMarketingCampaignCounts({school_id: 1} as GetMarketingCampaignCountsRequest)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Missing Parameters')
      }
    })
    test('throw error code 400 with message "Bad request" if the object has additional properties', async() => {
      try {
        await controller.getMarketingCampaignCounts({...params, test: true} as GetMarketingCampaignCountsRequest)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Bad Request')
      }
    })
    test('throw error code 500 if something is wrong with the database connection', async() => {
      jest.spyOn(marketingCampaignCommon, 'getMarketingCampaignsCount').mockImplementationOnce(()=> { throw expectedError })
      try {
        await controller.getMarketingCampaignCounts(params)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.INTERNAL_SERVER)
      }
    })
    test('when the campaignBreakdown,scholaMarketing,campaignEmail and campaignSMS counts are requested', async()=> {
      jest.spyOn(leadUtils, 'getLeadSourceCostCount').mockImplementationOnce(()=> Promise.resolve(10))
      jest.spyOn(salesforceUtils, 'getCaseCampaignCount').mockImplementationOnce(()=> Promise.resolve(20))
      jest.spyOn(marketingCampaignCommon, 'getMarketingCampaignsCount').mockImplementationOnce(()=> Promise.resolve(30))
      jest.spyOn(marketingCampaignCommon, 'getMarketingCampaignsCount').mockImplementationOnce(()=> Promise.resolve(40))
      try {
        const filtering = [
          {campaignEmail: {archived: true}},
          {campaignSMS: {archived: true}}
        ]
        const params_1: GetMarketingCampaignCountsRequest = {
          school_id: 1,
          ids:'campaignBreakdown,scholaMarketing,campaignEmail,campaignSMS',
          filtering: JSON.stringify(filtering)
        }
        const result = await controller.getMarketingCampaignCounts(params_1)
        expect(result).not.toBeUndefined()
        expect(result).not.toBeUndefined()
        expect(result.campaignBreakdown).toEqual(10)
        expect(result.scholaMarketing).toEqual(20)
        expect(result.campaignEmail).toEqual(30)
        expect(result.campaignSMS).toEqual(40)
      } catch (e) {
        expect(true).toBe(false)
      }
    })
    test('when a count is not supported (Test)', async()=> {
      try {
        const filtering = [
          {campaignEmail: {archived: true}},
          {campaignSMS: {archived: true}}
        ]
        const params_1: GetMarketingCampaignCountsRequest = {
          school_id: 1,
          ids:'Test',
          filtering: JSON.stringify(filtering)
        }
        const result = await controller.getMarketingCampaignCounts(params_1)
        expect(result).not.toBeUndefined()
        expect(result).not.toBeUndefined()
        expect(result.Test).not.toBeUndefined()
        expect(result.Test).toEqual(0)

      } catch (e) {
        expect(true).toBe(false)
      }
    })
  })
  describe('uploadEmailCampaignSchoolLogo', () => {
    const params: UploadEmailCampaignSchoolLogoRequest = {
      school_id: 1,
      campaign_id: 1,
      fileName: 'abc.pdf'
    }

    test('is a function', () => {
      expect(typeof controller.uploadEmailCampaignSchoolLogo).toBe('function')
    })

    test('throw error code 400 with message "Missing Parameters" if no object is provided', async() => {
      try {
        await controller.uploadEmailCampaignSchoolLogo(undefined)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Missing Parameters')
      }
    })
    test('throw error code 400 with message "Missing Parameters" if the object is empty', async() => {
      try {
        await controller.uploadEmailCampaignSchoolLogo({} as UploadEmailCampaignSchoolLogoRequest)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Missing Parameters')
      }
    })
    test('throw error code 400 with message "Missing Parameters" if the object is missing required properties', async() => {
      try {
        await controller.uploadEmailCampaignSchoolLogo({campaign_id: 1} as UploadEmailCampaignSchoolLogoRequest)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Missing Parameters')
      }
    })
    test('throw error code 400 with message "Bad request" if the object has additional properties', async() => {
      try {
        await controller.uploadEmailCampaignSchoolLogo({...params, test: true} as UploadEmailCampaignSchoolLogoRequest)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Bad Request')
      }
    })
  })
  describe('checkSchedules', () => {
    test('is a function', () => {
      expect(typeof controller.checkSchedules).toBe('function')
    })
    test('throw error code 500 if something is wrong with the database connection', async() => {
      jest.spyOn(marketingCampaignCheckSchedule, 'checkSchedules').mockImplementationOnce(()=> { throw expectedError })
      try {
        await controller.checkSchedules()
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.INTERNAL_SERVER)
      }
    })
    test('returns an response when check schedules return undefined', async() => {
      jest.spyOn(marketingCampaignCheckSchedule, 'checkSchedules').mockResolvedValueOnce(undefined)
      const result = await controller.checkSchedules()
      expect(result).not.toBeNull()
      expect(result.success).toEqual(false)
    })
    test('returns an response when check schedules return success', async() => {
      jest.spyOn(marketingCampaignCheckSchedule, 'checkSchedules').mockResolvedValueOnce(Promise.resolve(marketing_campaign_result_string_success))
      const result = await controller.checkSchedules()
      expect(result).not.toBeNull()
      expect(result.success).toEqual(true)
    })
    test('returns an response when check schedules return unsuccess', async() => {
      jest.spyOn(marketingCampaignCheckSchedule, 'checkSchedules').mockResolvedValueOnce(Promise.resolve(marketing_campaign_result_string_unsuccess))
      const result = await controller.checkSchedules()
      expect(result).not.toBeNull()
      expect(result.success).toEqual(false)
    })
  })
  describe('sendTest', () => {
    const params: SendTestMarketingCampaignRequest = {
      school_id: 1,
      lead: {
        email: '<EMAIL>',
        phone: '6121682584'
      },
      marketingCampaign:{
        id:1,
        school_id: 1,
        audience_language: 'en',
        layout_id: 1,
        marketing_template_id: 1,
        name: 'test',
        platform: 'email',
        type: 'Recurring Automated Email',
        params:[{
          key:'1',
          value: 'a'
        }]
      }
    }

    test('is a function', () => {
      expect(typeof controller.sendTest).toBe('function')
    })

    test('throw error code 400 with message "Missing Parameters" if no object is provided', async() => {
      try {
        await controller.sendTest(undefined)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Missing Parameters')
      }
    })
    test('throw error code 400 with message "Missing Parameters" if the object is empty', async() => {
      try {
        await controller.sendTest({} as SendTestMarketingCampaignRequest)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Missing Parameters')
      }
    })
    test('throw error code 400 with message "Missing Parameters" if the object is missing required properties', async() => {
      try {
        await controller.sendTest({marketingCampaign: {}} as SendTestMarketingCampaignRequest)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Missing Parameters')
      }
    })
    test('throw error code 400 with message "Bad request" if the object has additional properties', async() => {
      try {
        await controller.sendTest({...params, test: true} as SendTestMarketingCampaignRequest)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Bad Request')
      }
    })
    test('throw error code 500 if something is wrong with the database connection', async() => {
      jest.spyOn(marketingCampaignSendTest, 'processTest').mockImplementationOnce(()=> { throw expectedError })
      try {
        await controller.sendTest(params)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.INTERNAL_SERVER)
      }
    })
    test('returns an valid object in the result when the process is success', async() => {

      jest.spyOn(marketingCampaignSendTest, 'processTest').mockResolvedValueOnce(Promise.resolve(send_test_marketing_campaign_response_success))
      const result = await controller.sendTest(params)
      expect(result).not.toBeNull()
      expect(result.success).toEqual(true)
    })

    test('returns an valid object in the result when the process is unsuccess', async() => {

      jest.spyOn(marketingCampaignSendTest, 'processTest').mockResolvedValueOnce(Promise.resolve(send_test_marketing_campaign_response_unsuccess))
      const result = await controller.sendTest(params)
      expect(result).not.toBeNull()
      expect(result.success).toEqual(false)
    })
  })
  describe('getApplicationUrlForTest', () => {
    const params: GetApplicationUrlForTestMarketingCampaignRequest = {
      school_id: 1
    }

    test('is a function', () => {
      expect(typeof controller.getApplicationUrlForTest).toBe('function')
    })

    test('throw error code 400 with message "Missing Parameters" if no object is provided', async() => {
      try {
        await controller.getApplicationUrlForTest(undefined)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Missing Parameters')
      }
    })
    test('throw error code 400 with message "Missing Parameters" if the object is empty', async() => {
      try {
        await controller.getApplicationUrlForTest({} as GetApplicationUrlForTestMarketingCampaignRequest)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Missing Parameters')
      }
    })
    test('throw error code 400 with message "Bad request" if the object has additional properties', async() => {
      try {
        await controller.getApplicationUrlForTest({...params, test: true} as GetApplicationUrlForTestMarketingCampaignRequest)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Bad Request')
      }
    })
    test('throw error code 500 if something is wrong with the database connection on getSchoolById', async() => {
      jest.spyOn(marketingCampaignCommon, 'getSchoolById').mockImplementationOnce(()=> { throw expectedError })
      try {
        await controller.getApplicationUrlForTest(params)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.INTERNAL_SERVER)
      }
    })
    test('throw error code 500 if something is wrong with the database connection on getApplicationUrlForTest', async() => {
      jest.spyOn(marketingCampaignCommon, 'getSchoolById').mockResolvedValueOnce(Promise.resolve(school))
      jest.spyOn(applicationToLeadUtil, 'getApplicationUrlForTest').mockImplementationOnce(()=> { throw expectedError })
      try {
        await controller.getApplicationUrlForTest(params)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.INTERNAL_SERVER)
      }
    })
    test('throw error code 500 if something is wrong with the database connection on getApplicationUrlForTest', async() => {
      jest.spyOn(marketingCampaignCommon, 'getSchoolById').mockResolvedValueOnce(Promise.resolve(school))
      jest.spyOn(applicationToLeadUtil, 'getApplicationUrlForTest').mockImplementationOnce(()=> { throw expectedError })
      try {
        await controller.getApplicationUrlForTest(params)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.INTERNAL_SERVER)
      }
    })

    test('returns an valid object in the result', async() => {
      const application_url_for_test = 'https://test.com/test'
      jest.spyOn(marketingCampaignCommon, 'getSchoolById').mockResolvedValueOnce(Promise.resolve(school))
      jest.spyOn(applicationToLeadUtil,'getApplicationUrlForTest').mockResolvedValueOnce(Promise.resolve(application_url_for_test))
      const result = await controller.getApplicationUrlForTest(params)
      expect(result).not.toBeNull()
      expect(result.url).not.toBeUndefined()
      expect(result.url).toEqual(application_url_for_test)
    })
  })
  describe('getExecutionsByLead', () => {
    const params: GetExecutionsByLeadIdMarketingCampaignRequest = {
      school_id: 1,
      lead_id: 1
    }

    test('is a function', () => {
      expect(typeof controller.getExecutionsByLead).toBe('function')
    })

    test('throw error code 400 with message "Missing Parameters" if no object is provided', async() => {
      try {
        await controller.getExecutionsByLead(undefined)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Missing Parameters')
      }
    })
    test('throw error code 400 with message "Missing Parameters" if the object is empty', async() => {
      try {
        await controller.getExecutionsByLead({} as GetExecutionsByLeadIdMarketingCampaignRequest)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Missing Parameters')
      }
    })
    test('throw error code 400 with message "Missing Parameters" if the object is missing required properties', async() => {
      try {
        await controller.getExecutionsByLead({school_id: 1} as GetExecutionsByLeadIdMarketingCampaignRequest)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Missing Parameters')
      }
    })
    test('throw error code 400 with message "Bad request" if the object has additional properties', async() => {
      try {
        await controller.getExecutionsByLead({...params, test: true} as GetExecutionsByLeadIdMarketingCampaignRequest)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.BAD_REQUEST)
        expect(e.message).toBe('Bad Request')
      }
    })
    test('throw error code 500 if something is wrong with the database connection', async() => {
      jest.spyOn(marketingCampaignCommon, 'getExecutionsByLead').mockImplementationOnce(()=> { throw expectedError })
      try {
        await controller.getExecutionsByLead(params)
        expect(true).toBe(false)
      } catch (e) {
        expect(e.statusCode).toBe(http_status_codes.INTERNAL_SERVER)
      }
    })

    test('returns an valid object in the result', async() => {
      const get_executions_by_lead_id_marketing_campaign_response_array : GetExecutionsByLeadIdMarketingCampaignResponse[] = [
        {
          campaign: 1,
          created_at: moment.utc('2022-05-10T00:29:38.205Z').toDate(),
          marketing_campaign_executed_audit_id: 1,
          marketing_campaign_id: 1,
          status: 'Active',
          type: 'sms'
        },
        {
          campaign: 2,
          created_at: moment.utc('2022-05-10T00:29:38.205Z').toDate(),
          marketing_campaign_executed_audit_id: 2,
          marketing_campaign_id: 2,
          status: 'Active',
          type: 'email'
        }
      ]
      jest.spyOn(marketingCampaignCommon, 'getExecutionsByLead').mockResolvedValueOnce(Promise.resolve(get_executions_by_lead_id_marketing_campaign_response_array))
      const result = await controller.getExecutionsByLead(params)
      expect(result).not.toBeNull()
      expect(result).toStrictEqual(get_executions_by_lead_id_marketing_campaign_response_array)
    })

  })
})
